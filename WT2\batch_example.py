#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch Format Dataset Example
batch格式数据集使用示例

文件格式示例：
- batch5_22080717046_1.json / batch5_22080717046_1.png
- batch5_22080717046_2.json / batch5_22080717046_2.png
- batch5_22080717046_3.json / batch5_22080717046_3.png

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import json
import glob


def show_batch_format_info():
    """显示batch格式信息"""
    print("Batch格式数据集使用指南")
    print("=" * 50)
    
    print("\n支持的文件格式:")
    print("  图像文件: batch*.png, batch*.jpg, batch*.jpeg")
    print("  标签文件: batch*.json")
    
    print("\n文件命名示例:")
    examples = [
        "batch5_22080717046_1.png + batch5_22080717046_1.json",
        "batch5_22080717046_2.png + batch5_22080717046_2.json", 
        "batch6_22080717047_1.png + batch6_22080717047_1.json",
        "batch6_22080717047_2.png + batch6_22080717047_2.json"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"  {i}. {example}")


def show_directory_structure():
    """显示目录结构示例"""
    print("\n支持的目录结构:")
    
    print("\n1. 简单结构（推荐）:")
    print("your_dataset/")
    print("├── batch5_22080717046_1.png")
    print("├── batch5_22080717046_1.json")
    print("├── batch5_22080717046_2.png")
    print("├── batch5_22080717046_2.json")
    print("├── batch6_22080717047_1.png")
    print("├── batch6_22080717047_1.json")
    print("└── ...")
    
    print("\n2. 分类结构:")
    print("your_dataset/")
    print("├── train/")
    print("│   ├── batch5_22080717046_1.png")
    print("│   ├── batch5_22080717046_1.json")
    print("│   └── ...")
    print("├── val/")
    print("│   ├── batch6_22080717047_1.png")
    print("│   ├── batch6_22080717047_1.json")
    print("│   └── ...")
    print("└── test/")
    print("    ├── batch7_22080717048_1.png")
    print("    ├── batch7_22080717048_1.json")
    print("    └── ...")


def show_usage_commands():
    """显示使用命令"""
    print("\n使用命令示例:")
    
    print("\n1. 配置batch数据集:")
    print("   python batch_dataset_setup.py")
    
    print("\n2. 训练模型:")
    commands = [
        'python experiments/train_fourier_detector.py --data_root "D:\\MyBatchDataset"',
        'python experiments/train_fourier_detector.py --data_root "./batch_data" --epochs 50',
        'python experiments/train_fourier_detector.py --interactive'
    ]
    
    for i, cmd in enumerate(commands, 1):
        print(f"   {i}. {cmd}")
    
    print("\n3. 模型演示:")
    demo_commands = [
        'python experiments/demo.py --image_path "D:\\MyBatchDataset\\batch5_22080717046_1.png"',
        'python experiments/demo.py --image_path "D:\\MyBatchDataset\\" --save_dir results',
        'python experiments/demo.py --interactive'
    ]
    
    for i, cmd in enumerate(demo_commands, 1):
        print(f"   {i}. {cmd}")


def analyze_sample_dataset(dataset_path: str):
    """分析示例数据集"""
    if not os.path.exists(dataset_path):
        print(f"数据集路径不存在: {dataset_path}")
        return
    
    print(f"\n分析数据集: {dataset_path}")
    print("-" * 40)
    
    # 查找batch文件
    image_files = []
    for ext in ['.png', '.jpg', '.jpeg']:
        pattern = os.path.join(dataset_path, f"batch*{ext}")
        image_files.extend(glob.glob(pattern))
    
    label_files = glob.glob(os.path.join(dataset_path, "batch*.json"))
    
    print(f"图像文件数量: {len(image_files)}")
    print(f"标签文件数量: {len(label_files)}")
    
    # 检查匹配情况
    matched = 0
    unmatched_images = []
    unmatched_labels = []
    
    for img_file in image_files:
        base_name = os.path.splitext(os.path.basename(img_file))[0]
        label_file = os.path.join(dataset_path, base_name + '.json')
        
        if os.path.exists(label_file):
            matched += 1
        else:
            unmatched_images.append(os.path.basename(img_file))
    
    for label_file in label_files:
        base_name = os.path.splitext(os.path.basename(label_file))[0]
        img_extensions = ['.png', '.jpg', '.jpeg']
        has_image = False
        
        for ext in img_extensions:
            img_file = os.path.join(dataset_path, base_name + ext)
            if os.path.exists(img_file):
                has_image = True
                break
        
        if not has_image:
            unmatched_labels.append(os.path.basename(label_file))
    
    print(f"匹配的图像-标签对: {matched}")
    print(f"匹配率: {matched/len(image_files)*100:.1f}%" if image_files else "匹配率: 0%")
    
    if unmatched_images:
        print(f"\n缺少标签的图像 ({len(unmatched_images)}):")
        for img in unmatched_images[:5]:  # 只显示前5个
            print(f"  - {img}")
        if len(unmatched_images) > 5:
            print(f"  ... 还有 {len(unmatched_images)-5} 个")
    
    if unmatched_labels:
        print(f"\n缺少图像的标签 ({len(unmatched_labels)}):")
        for label in unmatched_labels[:5]:  # 只显示前5个
            print(f"  - {label}")
        if len(unmatched_labels) > 5:
            print(f"  ... 还有 {len(unmatched_labels)-5} 个")
    
    # 显示文件样本
    if image_files:
        print(f"\n文件样本:")
        samples = image_files[:3]
        for img_file in samples:
            base_name = os.path.splitext(os.path.basename(img_file))[0]
            label_file = base_name + '.json'
            label_path = os.path.join(dataset_path, label_file)
            status = "✓" if os.path.exists(label_path) else "✗"
            print(f"  {status} {os.path.basename(img_file)} <-> {label_file}")


def show_json_format_example():
    """显示JSON标签格式示例"""
    print("\nJSON标签文件格式示例:")
    print("-" * 30)
    
    example_json = {
        "image_name": "batch5_22080717046_1.png",
        "image_size": {
            "width": 1920,
            "height": 1080
        },
        "targets": [
            {
                "id": 1,
                "class": "small_target",
                "bbox": {
                    "x": 100,
                    "y": 200,
                    "width": 20,
                    "height": 15
                },
                "confidence": 1.0,
                "visible": True
            },
            {
                "id": 2,
                "class": "small_target", 
                "bbox": {
                    "x": 500,
                    "y": 300,
                    "width": 25,
                    "height": 18
                },
                "confidence": 1.0,
                "visible": True
            }
        ]
    }
    
    print(json.dumps(example_json, indent=2, ensure_ascii=False))


def main():
    """主函数"""
    print("Batch格式数据集使用示例")
    print("=" * 60)
    
    while True:
        print("\n请选择要查看的内容:")
        print("1. Batch格式信息")
        print("2. 目录结构示例")
        print("3. 使用命令示例")
        print("4. 分析数据集")
        print("5. JSON格式示例")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == '1':
            show_batch_format_info()
        elif choice == '2':
            show_directory_structure()
        elif choice == '3':
            show_usage_commands()
        elif choice == '4':
            dataset_path = input("\n请输入数据集路径: ").strip().strip('"\'')
            if dataset_path:
                analyze_sample_dataset(dataset_path)
            else:
                print("路径不能为空")
        elif choice == '5':
            show_json_format_example()
        elif choice == '6':
            print("退出示例程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
