#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration File for Fourier-based Infrared Small Target Detection
基于傅里叶分析的红外小目标检测配置文件

包含所有模型、训练、数据处理的配置参数

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
from typing import Dict, List, Tuple, Any


class Config:
    """配置类"""
    
    # ==================== 基础配置 ====================
    PROJECT_NAME = "Fourier Infrared Small Target Detection"
    VERSION = "1.0.0"
    AUTHOR = "AI Assistant"
    
    # 项目路径
    PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
    DATA_ROOT = os.path.join(PROJECT_ROOT, "data")
    RESULTS_ROOT = os.path.join(PROJECT_ROOT, "results")
    CHECKPOINTS_ROOT = os.path.join(PROJECT_ROOT, "checkpoints")
    
    # ==================== 模型配置 ====================
    MODEL_CONFIG = {
        # 基础参数
        'input_channels': 1,  # 灰度图像
        'num_classes': 1,     # 小目标检测（二分类）
        'base_channels': 64,  # 基础通道数
        
        # FAN层配置
        'fan_config': {
            'num_frequencies': 8,      # 频率数量
            'temperature': 1.0,        # 温度参数
            'scales': [1, 2, 4, 8],   # 多尺度
        },
        
        # 频域注意力配置
        'attention_config': {
            'reduction': 16,           # 通道缩减比例
            'num_filters': 8,          # 自适应滤波器数量
        },
        
        # 检测头配置
        'detection_head_config': {
            'num_anchors': 9,          # 锚框数量
            'anchor_scales': [8, 16, 32],  # 锚框尺度
            'anchor_ratios': [0.5, 1.0, 2.0],  # 锚框比例
        }
    }
    
    # ==================== 训练配置 ====================
    TRAIN_CONFIG = {
        # 基础训练参数
        'epochs': 100,
        'batch_size': 8,
        'learning_rate': 1e-4,
        'weight_decay': 1e-4,
        'min_lr': 1e-6,
        
        # 优化器配置
        'optimizer': 'adamw',
        'optimizer_params': {
            'betas': (0.9, 0.999),
            'eps': 1e-8,
        },
        
        # 学习率调度器
        'scheduler': 'cosine',
        'scheduler_params': {
            'T_max': 100,
            'eta_min': 1e-6,
        },
        
        # 损失函数权重
        'loss_weights': {
            'cls_weight': 1.0,
            'reg_weight': 1.0,
            'center_weight': 0.5,
        },
        
        # Focal Loss参数
        'focal_loss_params': {
            'alpha': 0.25,
            'gamma': 2.0,
        },
        
        # 梯度裁剪
        'grad_clip_norm': 1.0,
        
        # 验证和保存
        'val_interval': 1,        # 验证间隔
        'save_interval': 10,      # 保存间隔
        'max_keep_ckpts': 5,      # 最大保存检查点数
    }
    
    # ==================== 数据配置 ====================
    DATA_CONFIG = {
        # 数据集路径
        'train_data_root': os.path.join(DATA_ROOT, 'train'),
        'val_data_root': os.path.join(DATA_ROOT, 'val'),
        'test_data_root': os.path.join(DATA_ROOT, 'test'),
        
        # 图像配置
        'image_size': (256, 256),  # 输入图像尺寸
        'image_channels': 1,       # 图像通道数
        'image_format': 'grayscale',  # 图像格式
        
        # 数据加载
        'num_workers': 4,
        'pin_memory': True,
        'drop_last': True,
        
        # 数据增强
        'augmentation': {
            'enable': True,
            'brightness_range': (0.8, 1.2),
            'contrast_range': (0.8, 1.2),
            'noise_intensity': 0.02,
            'blur_range': (0.5, 2.0),
            'rotation_range': (-10, 10),
            'flip_prob': 0.5,
        }
    }
    
    # ==================== 预处理配置 ====================
    PREPROCESSING_CONFIG = {
        # 图像增强
        'enhance_contrast': True,
        'contrast_alpha': 1.2,
        'contrast_beta': 0.0,
        
        # 伽马校正
        'gamma_correction': True,
        'gamma_value': 0.8,
        
        # 直方图均衡化
        'histogram_equalization': True,
        'clahe_clip_limit': 2.0,
        'clahe_tile_grid_size': (8, 8),
        
        # 去噪
        'denoise': True,
        'denoise_method': 'bilateral',  # 'bilateral', 'gaussian', 'nlm'
        'bilateral_params': {
            'd': 9,
            'sigma_color': 75,
            'sigma_space': 75,
        },
        
        # 小目标增强
        'enhance_small_targets': True,
        'tophat_kernel_size': 15,
        'laplacian_strength': 0.5,
        'unsharp_sigma': 1.0,
        'unsharp_strength': 1.5,
    }
    
    # ==================== 后处理配置 ====================
    POSTPROCESSING_CONFIG = {
        # 检测阈值
        'conf_threshold': 0.5,
        'nms_threshold': 0.5,
        'max_detections': 100,
        
        # 目标尺寸过滤
        'min_target_size': 5,      # 最小目标尺寸
        'max_target_size': 200,    # 最大目标尺寸
        'min_aspect_ratio': 0.2,   # 最小宽高比
        'max_aspect_ratio': 5.0,   # 最大宽高比
        
        # 多尺度测试
        'multi_scale_test': False,
        'test_scales': [0.8, 1.0, 1.2],
    }
    
    # ==================== 评估配置 ====================
    EVALUATION_CONFIG = {
        # 评估指标
        'metrics': ['precision', 'recall', 'f1', 'ap', 'map'],
        
        # IoU阈值
        'iou_thresholds': [0.3, 0.5, 0.7],
        'map_iou_range': (0.3, 0.8, 0.05),  # (start, end, step)
        
        # 目标尺寸分类
        'size_ranges': {
            'small': (0, 32),      # 小目标
            'medium': (32, 96),    # 中等目标
            'large': (96, float('inf')),  # 大目标
        }
    }
    
    # ==================== 可视化配置 ====================
    VISUALIZATION_CONFIG = {
        # 颜色配置
        'colors': {
            'detection': (0, 255, 0),      # 检测框颜色（绿色）
            'ground_truth': (255, 0, 0),   # 真实框颜色（红色）
            'text': (255, 255, 255),       # 文字颜色（白色）
        },
        
        # 绘制参数
        'line_thickness': 2,
        'font_scale': 0.5,
        'font_thickness': 1,
        
        # 保存配置
        'save_format': 'png',
        'save_dpi': 150,
        'save_quality': 95,
        
        # 频域可视化
        'show_frequency_analysis': True,
        'frequency_colormap': 'gray',
    }
    
    # ==================== 设备配置 ====================
    DEVICE_CONFIG = {
        # 设备选择
        'device': 'auto',  # 'auto', 'cpu', 'cuda', 'cuda:0'
        
        # CUDA配置
        'cuda_benchmark': True,
        'cuda_deterministic': False,
        
        # 内存配置
        'max_memory_fraction': 0.9,
        'memory_growth': True,
    }
    
    # ==================== 日志配置 ====================
    LOGGING_CONFIG = {
        # 日志级别
        'level': 'INFO',  # 'DEBUG', 'INFO', 'WARNING', 'ERROR'
        
        # 日志格式
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'date_format': '%Y-%m-%d %H:%M:%S',
        
        # 日志文件
        'log_file': os.path.join(RESULTS_ROOT, 'training.log'),
        'max_log_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
        
        # TensorBoard
        'tensorboard_dir': os.path.join(RESULTS_ROOT, 'tensorboard'),
        'log_interval': 10,  # 记录间隔
    }
    
    @classmethod
    def get_config(cls, config_name: str = None) -> Dict[str, Any]:
        """
        获取配置
        Args:
            config_name: 配置名称，如果为None则返回所有配置
        Returns:
            配置字典
        """
        if config_name is None:
            return {
                'model': cls.MODEL_CONFIG,
                'train': cls.TRAIN_CONFIG,
                'data': cls.DATA_CONFIG,
                'preprocessing': cls.PREPROCESSING_CONFIG,
                'postprocessing': cls.POSTPROCESSING_CONFIG,
                'evaluation': cls.EVALUATION_CONFIG,
                'visualization': cls.VISUALIZATION_CONFIG,
                'device': cls.DEVICE_CONFIG,
                'logging': cls.LOGGING_CONFIG,
            }
        
        config_map = {
            'model': cls.MODEL_CONFIG,
            'train': cls.TRAIN_CONFIG,
            'data': cls.DATA_CONFIG,
            'preprocessing': cls.PREPROCESSING_CONFIG,
            'postprocessing': cls.POSTPROCESSING_CONFIG,
            'evaluation': cls.EVALUATION_CONFIG,
            'visualization': cls.VISUALIZATION_CONFIG,
            'device': cls.DEVICE_CONFIG,
            'logging': cls.LOGGING_CONFIG,
        }
        
        return config_map.get(config_name, {})
    
    @classmethod
    def update_config(cls, config_name: str, updates: Dict[str, Any]):
        """
        更新配置
        Args:
            config_name: 配置名称
            updates: 更新内容
        """
        config_map = {
            'model': cls.MODEL_CONFIG,
            'train': cls.TRAIN_CONFIG,
            'data': cls.DATA_CONFIG,
            'preprocessing': cls.PREPROCESSING_CONFIG,
            'postprocessing': cls.POSTPROCESSING_CONFIG,
            'evaluation': cls.EVALUATION_CONFIG,
            'visualization': cls.VISUALIZATION_CONFIG,
            'device': cls.DEVICE_CONFIG,
            'logging': cls.LOGGING_CONFIG,
        }
        
        if config_name in config_map:
            config_map[config_name].update(updates)
        else:
            raise ValueError(f"Unknown config name: {config_name}")
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.DATA_ROOT,
            cls.RESULTS_ROOT,
            cls.CHECKPOINTS_ROOT,
            os.path.join(cls.DATA_ROOT, 'train'),
            os.path.join(cls.DATA_ROOT, 'val'),
            os.path.join(cls.DATA_ROOT, 'test'),
            os.path.join(cls.RESULTS_ROOT, 'visualizations'),
            os.path.join(cls.RESULTS_ROOT, 'tensorboard'),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        print(f"✅ 创建项目目录结构完成")


# 默认配置实例
default_config = Config()

# 创建目录
if __name__ == "__main__":
    Config.create_directories()
    
    # 打印配置信息
    print("🔧 傅里叶红外小目标检测配置")
    print("=" * 50)
    
    all_configs = Config.get_config()
    for config_name, config_dict in all_configs.items():
        print(f"\n📋 {config_name.upper()} 配置:")
        for key, value in config_dict.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
    
    print("\n🎉 配置加载完成！")
