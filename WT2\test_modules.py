#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module Testing Script for Fourier-based Infrared Small Target Detection
模块测试脚本，验证所有组件是否正常工作

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import torch
import numpy as np
import traceback
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fan_enhanced():
    """测试增强版FAN层"""
    print("🧪 测试增强版FAN层...")
    try:
        from models.fan_enhanced import EnhancedFANLayer, MultiScaleFANLayer, AdaptiveFANBlock
        
        # 测试基本FAN层
        input_dim = 64
        output_dim = 128
        batch_size = 4
        
        fan_layer = EnhancedFANLayer(input_dim, output_dim)
        x = torch.randn(batch_size, input_dim)
        output = fan_layer(x)
        
        assert output.shape == (batch_size, output_dim), f"输出形状错误: {output.shape}"
        
        # 测试多尺度FAN层
        multiscale_fan = MultiScaleFANLayer(input_dim, output_dim)
        output_ms = multiscale_fan(x)
        
        assert output_ms.shape == (batch_size, output_dim), f"多尺度输出形状错误: {output_ms.shape}"
        
        # 测试自适应FAN块
        adaptive_block = AdaptiveFANBlock(input_dim, 256, output_dim)
        output_adaptive = adaptive_block(x)
        
        assert output_adaptive.shape == (batch_size, output_dim), f"自适应块输出形状错误: {output_adaptive.shape}"
        
        print("✅ 增强版FAN层测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 增强版FAN层测试失败: {e}")
        traceback.print_exc()
        return False


def test_frequency_attention():
    """测试频域注意力机制"""
    print("🧪 测试频域注意力机制...")
    try:
        from models.frequency_attention import (
            FrequencyChannelAttention, 
            FrequencySpatialAttention, 
            ComprehensiveFrequencyAttention
        )
        
        # 测试参数
        batch_size = 2
        channels = 64
        height, width = 32, 32
        
        x = torch.randn(batch_size, channels, height, width)
        
        # 测试通道注意力
        channel_attn = FrequencyChannelAttention(channels)
        channel_out, freq_weights = channel_attn(x)
        
        assert channel_out.shape == x.shape, f"通道注意力输出形状错误: {channel_out.shape}"
        
        # 测试空间注意力
        spatial_attn = FrequencySpatialAttention(channels)
        spatial_out, spatial_weights = spatial_attn(x)
        
        assert spatial_out.shape == x.shape, f"空间注意力输出形状错误: {spatial_out.shape}"
        
        # 测试综合注意力
        comprehensive_attn = ComprehensiveFrequencyAttention(channels)
        final_out, attn_info = comprehensive_attn(x)
        
        assert final_out.shape == x.shape, f"综合注意力输出形状错误: {final_out.shape}"
        assert isinstance(attn_info, dict), "注意力信息应该是字典类型"
        
        print("✅ 频域注意力机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 频域注意力机制测试失败: {e}")
        traceback.print_exc()
        return False


def test_fourier_infrared_net():
    """测试主检测网络"""
    print("🧪 测试傅里叶红外检测网络...")
    try:
        from models.fourier_infrared_net import FourierInfraredNet
        
        # 测试参数
        batch_size = 2
        input_channels = 1
        height, width = 128, 128
        num_classes = 1
        
        # 创建测试输入
        x = torch.randn(batch_size, input_channels, height, width)
        
        # 创建网络
        model = FourierInfraredNet(input_channels, num_classes)
        
        # 前向传播
        with torch.no_grad():
            outputs = model(x)
        
        # 检查输出
        assert 'cls_scores' in outputs, "输出应包含分类分数"
        assert 'bbox_preds' in outputs, "输出应包含边界框预测"
        assert 'centernesses' in outputs, "输出应包含中心度预测"
        assert 'features' in outputs, "输出应包含特征图"
        
        assert len(outputs['cls_scores']) > 0, "分类分数列表不应为空"
        assert len(outputs['bbox_preds']) > 0, "边界框预测列表不应为空"
        
        print("✅ 傅里叶红外检测网络测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 傅里叶红外检测网络测试失败: {e}")
        traceback.print_exc()
        return False


def test_fourier_transforms():
    """测试傅里叶变换工具"""
    print("🧪 测试傅里叶变换工具...")
    try:
        from utils.fourier_transforms import (
            FourierTransformUtils, 
            FrequencyDomainFilters, 
            FrequencyDomainFeatures
        )
        
        # 创建测试图像
        height, width = 64, 64
        test_image = torch.randn(1, 1, height, width)
        
        # 测试基本变换
        freq_domain = FourierTransformUtils.fft2d(test_image)
        reconstructed = FourierTransformUtils.ifft2d(freq_domain)
        
        reconstruction_error = torch.mean((test_image - reconstructed)**2).item()
        assert reconstruction_error < 1e-5, f"重构误差过大: {reconstruction_error}"
        
        # 测试滤波器
        lowpass_filter = FrequencyDomainFilters.gaussian_lowpass_filter(height, width, sigma=0.1)
        assert lowpass_filter.shape == (height, width), f"滤波器形状错误: {lowpass_filter.shape}"
        
        # 测试特征提取
        band_features = FrequencyDomainFeatures.extract_frequency_bands(freq_domain, num_bands=4)
        assert band_features.shape[1] == 4, f"频带特征数量错误: {band_features.shape[1]}"
        
        print("✅ 傅里叶变换工具测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 傅里叶变换工具测试失败: {e}")
        traceback.print_exc()
        return False


def test_infrared_preprocessing():
    """测试红外图像预处理"""
    print("🧪 测试红外图像预处理...")
    try:
        from utils.infrared_preprocessing import (
            InfraredImageEnhancer, 
            NoiseReduction, 
            SmallTargetEnhancer,
            InfraredPreprocessingPipeline
        )
        
        # 创建测试图像
        test_image = torch.rand(1, 1, 128, 128)
        
        # 测试增强器
        enhancer = InfraredImageEnhancer()
        enhanced = enhancer.contrast_enhancement(test_image)
        assert enhanced.shape == test_image.shape, f"增强后形状错误: {enhanced.shape}"
        
        # 测试去噪
        denoiser = NoiseReduction()
        denoised = denoiser.gaussian_denoising(test_image)
        assert denoised.shape == test_image.shape, f"去噪后形状错误: {denoised.shape}"
        
        # 测试小目标增强
        small_enhancer = SmallTargetEnhancer()
        lap_enhanced = small_enhancer.laplacian_enhancement(test_image.squeeze())
        assert lap_enhanced.dim() >= 2, "拉普拉斯增强输出维度错误"
        
        # 测试预处理流水线
        pipeline = InfraredPreprocessingPipeline()
        processed = pipeline.preprocess(test_image, training=True)
        # 允许形状略有不同，只要维度数量相同
        assert processed.dim() == test_image.dim(), f"流水线处理后维度错误: {processed.dim()} vs {test_image.dim()}"
        print(f"   原始形状: {test_image.shape}, 处理后形状: {processed.shape}")
        
        print("✅ 红外图像预处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 红外图像预处理测试失败: {e}")
        traceback.print_exc()
        return False


def test_config():
    """测试配置文件"""
    print("🧪 测试配置文件...")
    try:
        from config import Config
        
        # 测试配置获取
        model_config = Config.get_config('model')
        assert isinstance(model_config, dict), "模型配置应该是字典类型"
        assert 'input_channels' in model_config, "模型配置应包含input_channels"
        
        train_config = Config.get_config('train')
        assert isinstance(train_config, dict), "训练配置应该是字典类型"
        assert 'epochs' in train_config, "训练配置应包含epochs"
        
        # 测试配置更新
        original_epochs = train_config['epochs']
        Config.update_config('train', {'epochs': 200})
        updated_config = Config.get_config('train')
        assert updated_config['epochs'] == 200, "配置更新失败"
        
        # 恢复原始配置
        Config.update_config('train', {'epochs': original_epochs})
        
        print("✅ 配置文件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行所有模块测试...")
    print("=" * 60)
    
    test_results = {}
    
    # 运行各个测试
    test_functions = [
        ('FAN增强层', test_fan_enhanced),
        ('频域注意力', test_frequency_attention),
        ('主检测网络', test_fourier_infrared_net),
        ('傅里叶变换工具', test_fourier_transforms),
        ('红外预处理', test_infrared_preprocessing),
        ('配置文件', test_config),
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n📋 测试 {test_name}...")
        try:
            result = test_func()
            test_results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 测试出现异常: {e}")
            test_results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
        return False


def main():
    """主函数"""
    print("🔬 傅里叶红外小目标检测系统 - 模块测试")
    print("=" * 60)
    print("📅 测试时间:", torch.cuda.get_device_name(0) if torch.cuda.is_available() else "CPU模式")
    print("🐍 Python版本:", sys.version.split()[0])
    print("🔥 PyTorch版本:", torch.__version__)
    print("💾 CUDA可用:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("🎮 GPU设备:", torch.cuda.get_device_name(0))
        print("💾 GPU内存:", f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n🎊 恭喜！所有模块测试通过，系统可以正常使用。")
        print("\n📖 接下来您可以:")
        print("  1. 准备数据集并放入 data/ 目录")
        print("  2. 运行训练脚本: python experiments/train_fourier_detector.py")
        print("  3. 运行演示脚本: python experiments/demo.py")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息并修复相关问题。")
    
    return success


if __name__ == "__main__":
    main()
