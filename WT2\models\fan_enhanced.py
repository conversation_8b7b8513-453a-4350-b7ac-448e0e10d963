#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Fourier Analysis Networks (FAN) for Infrared Small Target Detection
基于傅里叶分析的增强神经网络层，专门针对红外小目标检测优化

主要创新点：
1. 频域特征增强 - 利用傅里叶变换捕获周期性和频域特征
2. 多频率分析 - 同时处理不同频率成分
3. 红外图像适配 - 针对红外图像的特殊性质优化
4. 小目标敏感 - 增强对小目标的检测能力

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math


class EnhancedFANLayer(nn.Module):
    """
    增强版傅里叶分析网络层
    专门为红外小目标检测设计的FAN层
    """
    
    def __init__(self, input_dim, output_dim, num_frequencies=8, temperature=1.0):
        super(EnhancedFANLayer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_frequencies = num_frequencies
        self.temperature = temperature
        
        # 计算各部分维度
        self.d_fourier = output_dim // 3  # 傅里叶部分
        self.d_nonlinear = output_dim // 3  # 非线性部分
        self.d_residual = output_dim - self.d_fourier - self.d_nonlinear  # 残差部分
        
        # 傅里叶变换参数
        self.W_cos = nn.Parameter(torch.Tensor(input_dim, self.d_fourier))
        self.W_sin = nn.Parameter(torch.Tensor(input_dim, self.d_fourier))
        self.frequencies = nn.Parameter(torch.Tensor(self.d_fourier))
        
        # 非线性变换参数
        self.W_nonlinear = nn.Parameter(torch.Tensor(input_dim, self.d_nonlinear))
        self.B_nonlinear = nn.Parameter(torch.Tensor(self.d_nonlinear))
        
        # 残差连接参数
        self.W_residual = nn.Parameter(torch.Tensor(input_dim, self.d_residual))
        
        # 频域注意力机制
        self.freq_attention = nn.Sequential(
            nn.Linear(self.d_fourier, self.d_fourier // 4),
            nn.ReLU(),
            nn.Linear(self.d_fourier // 4, self.d_fourier),
            nn.Sigmoid()
        )
        
        self._initialize_parameters()
    
    def _initialize_parameters(self):
        """初始化参数"""
        # Xavier初始化
        nn.init.xavier_uniform_(self.W_cos)
        nn.init.xavier_uniform_(self.W_sin)
        nn.init.xavier_uniform_(self.W_nonlinear)
        nn.init.xavier_uniform_(self.W_residual)
        
        # 频率参数初始化为不同的频率值
        with torch.no_grad():
            self.frequencies.data = torch.linspace(0.1, 10.0, self.d_fourier)
        
        # 偏置初始化
        nn.init.zeros_(self.B_nonlinear)
    
    def forward(self, x):
        """前向传播"""
        batch_size = x.size(0)

        # 1. 傅里叶变换部分
        cos_input = F.linear(x, self.W_cos.T)  # 修正矩阵乘法
        sin_input = F.linear(x, self.W_sin.T)  # 修正矩阵乘法

        cos_input = cos_input * self.frequencies.unsqueeze(0)
        sin_input = sin_input * self.frequencies.unsqueeze(0)

        cos_term = torch.cos(cos_input / self.temperature)
        sin_term = torch.sin(sin_input / self.temperature)

        # 傅里叶特征组合
        fourier_features = cos_term + sin_term

        # 频域注意力
        freq_weights = self.freq_attention(fourier_features)
        fourier_features = fourier_features * freq_weights

        # 2. 非线性变换部分
        nonlinear_features = F.linear(x, self.W_nonlinear.T, self.B_nonlinear)  # 修正矩阵乘法
        nonlinear_features = F.gelu(nonlinear_features)

        # 3. 残差连接部分
        residual_features = F.linear(x, self.W_residual.T)  # 修正矩阵乘法

        # 4. 特征融合
        output = torch.cat([fourier_features, nonlinear_features, residual_features], dim=-1)

        return output


class MultiScaleFANLayer(nn.Module):
    """
    多尺度傅里叶分析网络层
    处理不同尺度的特征，适合小目标检测
    """
    
    def __init__(self, input_dim, output_dim, scales=[1, 2, 4, 8]):
        super(MultiScaleFANLayer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.scales = scales
        self.num_scales = len(scales)
        
        # 每个尺度的输出维度
        self.scale_dim = output_dim // self.num_scales
        self.remaining_dim = output_dim - self.scale_dim * (self.num_scales - 1)
        
        # 为每个尺度创建FAN层
        self.scale_layers = nn.ModuleList()
        for i, scale in enumerate(scales):
            if i == len(scales) - 1:  # 最后一个尺度处理剩余维度
                layer_output_dim = self.remaining_dim
            else:
                layer_output_dim = self.scale_dim
            
            layer = EnhancedFANLayer(
                input_dim=input_dim,
                output_dim=layer_output_dim,
                num_frequencies=max(4, layer_output_dim // 4),
                temperature=1.0 / scale
            )
            self.scale_layers.append(layer)
        
        # 尺度融合层
        self.scale_fusion = nn.Sequential(
            nn.Linear(output_dim, output_dim),
            nn.LayerNorm(output_dim),
            nn.GELU()
        )
    
    def forward(self, x):
        """前向传播"""
        scale_outputs = []
        
        # 处理每个尺度
        for layer in self.scale_layers:
            scale_output = layer(x)
            scale_outputs.append(scale_output)
        
        # 拼接所有尺度的输出
        multi_scale_features = torch.cat(scale_outputs, dim=-1)
        
        # 尺度融合
        output = self.scale_fusion(multi_scale_features)
        
        return output


class FrequencyDomainProcessor(nn.Module):
    """
    频域处理器
    直接在频域进行特征处理，增强频域信息
    """
    
    def __init__(self, feature_dim):
        super(FrequencyDomainProcessor, self).__init__()
        
        self.feature_dim = feature_dim
        
        # 频域滤波器
        self.freq_filters = nn.Parameter(torch.Tensor(feature_dim, feature_dim))
        
        # 频域增强网络
        self.freq_enhancer = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),  # *2 for real and imaginary parts
            nn.ReLU(),
            nn.Linear(feature_dim, feature_dim),
            nn.Sigmoid()
        )
        
        self._initialize_parameters()
    
    def _initialize_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.freq_filters)
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入特征 [batch_size, feature_dim]
        Returns:
            增强后的特征
        """
        batch_size = x.size(0)
        
        # 1. 傅里叶变换到频域
        x_fft = torch.fft.fft(x, dim=-1)
        
        # 2. 分离实部和虚部
        x_real = x_fft.real
        x_imag = x_fft.imag
        
        # 3. 频域滤波
        filtered_real = F.linear(x_real, self.freq_filters)
        filtered_imag = F.linear(x_imag, self.freq_filters)
        
        # 4. 频域增强
        freq_input = torch.cat([filtered_real, filtered_imag], dim=-1)
        enhancement_weights = self.freq_enhancer(freq_input)
        
        # 5. 应用增强权重
        enhanced_real = filtered_real * enhancement_weights
        enhanced_imag = filtered_imag * enhancement_weights
        
        # 6. 重构复数并逆变换
        enhanced_fft = torch.complex(enhanced_real, enhanced_imag)
        enhanced_features = torch.fft.ifft(enhanced_fft, dim=-1).real
        
        return enhanced_features


class AdaptiveFANBlock(nn.Module):
    """
    自适应傅里叶分析块
    结合多尺度FAN和频域处理的完整模块
    """
    
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(AdaptiveFANBlock, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
        # 输入投影
        self.input_proj = nn.Linear(input_dim, hidden_dim)
        
        # 多尺度FAN层
        self.multiscale_fan = MultiScaleFANLayer(hidden_dim, hidden_dim)
        
        # 频域处理器
        self.freq_processor = FrequencyDomainProcessor(hidden_dim)
        
        # 输出投影
        self.output_proj = nn.Linear(hidden_dim, output_dim)
        
        # 残差连接和层归一化
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        """前向传播"""
        # 输入投影
        x = self.input_proj(x)
        residual = x
        
        # 多尺度FAN处理
        x = self.layer_norm1(x)
        x = self.multiscale_fan(x)
        x = self.dropout(x) + residual
        
        # 频域处理
        residual = x
        x = self.layer_norm2(x)
        x = self.freq_processor(x)
        x = self.dropout(x) + residual
        
        # 输出投影
        x = self.output_proj(x)
        
        return x


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试增强版FAN层...")
    
    # 测试基本FAN层
    input_dim = 64
    output_dim = 128
    batch_size = 16
    
    fan_layer = EnhancedFANLayer(input_dim, output_dim)
    x = torch.randn(batch_size, input_dim)
    output = fan_layer(x)
    
    print(f"✅ EnhancedFANLayer测试通过")
    print(f"   输入形状: {x.shape}")
    print(f"   输出形状: {output.shape}")
    
    # 测试多尺度FAN层
    multiscale_fan = MultiScaleFANLayer(input_dim, output_dim)
    output_ms = multiscale_fan(x)
    
    print(f"✅ MultiScaleFANLayer测试通过")
    print(f"   输出形状: {output_ms.shape}")
    
    # 测试自适应FAN块
    adaptive_block = AdaptiveFANBlock(input_dim, 256, output_dim)
    output_adaptive = adaptive_block(x)
    
    print(f"✅ AdaptiveFANBlock测试通过")
    print(f"   输出形状: {output_adaptive.shape}")
    
    print("🎉 所有测试通过！")
