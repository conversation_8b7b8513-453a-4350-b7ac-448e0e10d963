#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fourier Transform Utilities for Infrared Small Target Detection
傅里叶变换工具函数，用于红外小目标检测

主要功能：
1. 频域变换工具 - FFT、IFFT、频谱分析
2. 频域滤波器 - 各种频域滤波器实现
3. 频域特征提取 - 从频域提取有用特征
4. 频域可视化 - 频域分析结果可视化

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import cv2
from typing import Tuple, Optional, Union


class FourierTransformUtils:
    """傅里叶变换工具类"""
    
    @staticmethod
    def fft2d(image: torch.Tensor, normalized: bool = True) -> torch.Tensor:
        """
        2D傅里叶变换
        Args:
            image: 输入图像 [B, C, H, W] 或 [H, W]
            normalized: 是否归一化
        Returns:
            复数频域表示
        """
        if normalized:
            return torch.fft.fft2(image, norm='ortho')
        else:
            return torch.fft.fft2(image)
    
    @staticmethod
    def ifft2d(freq_domain: torch.Tensor, normalized: bool = True) -> torch.Tensor:
        """
        2D逆傅里叶变换
        Args:
            freq_domain: 频域复数表示
            normalized: 是否归一化
        Returns:
            重构的图像
        """
        if normalized:
            return torch.fft.ifft2(freq_domain, norm='ortho').real
        else:
            return torch.fft.ifft2(freq_domain).real
    
    @staticmethod
    def fftshift(freq_domain: torch.Tensor) -> torch.Tensor:
        """将零频率分量移到中心"""
        return torch.fft.fftshift(freq_domain, dim=(-2, -1))
    
    @staticmethod
    def ifftshift(freq_domain: torch.Tensor) -> torch.Tensor:
        """将零频率分量从中心移回"""
        return torch.fft.ifftshift(freq_domain, dim=(-2, -1))
    
    @staticmethod
    def get_magnitude_spectrum(freq_domain: torch.Tensor, log_scale: bool = True) -> torch.Tensor:
        """
        获取幅度谱
        Args:
            freq_domain: 频域复数表示
            log_scale: 是否使用对数尺度
        Returns:
            幅度谱
        """
        magnitude = torch.abs(freq_domain)
        if log_scale:
            magnitude = torch.log(magnitude + 1e-8)
        return magnitude
    
    @staticmethod
    def get_phase_spectrum(freq_domain: torch.Tensor) -> torch.Tensor:
        """获取相位谱"""
        return torch.angle(freq_domain)
    
    @staticmethod
    def create_frequency_grid(height: int, width: int, device: torch.device = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        创建频率网格
        Args:
            height: 图像高度
            width: 图像宽度
            device: 设备
        Returns:
            (freq_x, freq_y): x和y方向的频率网格
        """
        if device is None:
            device = torch.device('cpu')
        
        freq_x = torch.fft.fftfreq(width, device=device).view(1, width)
        freq_y = torch.fft.fftfreq(height, device=device).view(height, 1)
        
        return freq_x, freq_y
    
    @staticmethod
    def create_radial_frequency_grid(height: int, width: int, device: torch.device = None) -> torch.Tensor:
        """
        创建径向频率网格
        Args:
            height: 图像高度
            width: 图像宽度
            device: 设备
        Returns:
            径向频率网格
        """
        freq_x, freq_y = FourierTransformUtils.create_frequency_grid(height, width, device)
        radial_freq = torch.sqrt(freq_x**2 + freq_y**2)
        return radial_freq


class FrequencyDomainFilters:
    """频域滤波器集合"""
    
    @staticmethod
    def ideal_lowpass_filter(height: int, width: int, cutoff: float, device: torch.device = None) -> torch.Tensor:
        """
        理想低通滤波器
        Args:
            height, width: 滤波器尺寸
            cutoff: 截止频率
            device: 设备
        Returns:
            滤波器
        """
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        filter_mask = (radial_freq <= cutoff).float()
        return filter_mask
    
    @staticmethod
    def ideal_highpass_filter(height: int, width: int, cutoff: float, device: torch.device = None) -> torch.Tensor:
        """理想高通滤波器"""
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        filter_mask = (radial_freq >= cutoff).float()
        return filter_mask
    
    @staticmethod
    def ideal_bandpass_filter(height: int, width: int, low_cutoff: float, high_cutoff: float, device: torch.device = None) -> torch.Tensor:
        """理想带通滤波器"""
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        filter_mask = ((radial_freq >= low_cutoff) & (radial_freq <= high_cutoff)).float()
        return filter_mask
    
    @staticmethod
    def gaussian_lowpass_filter(height: int, width: int, sigma: float, device: torch.device = None) -> torch.Tensor:
        """
        高斯低通滤波器
        Args:
            height, width: 滤波器尺寸
            sigma: 高斯标准差
            device: 设备
        Returns:
            滤波器
        """
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        filter_mask = torch.exp(-0.5 * (radial_freq / sigma)**2)
        return filter_mask
    
    @staticmethod
    def gaussian_highpass_filter(height: int, width: int, sigma: float, device: torch.device = None) -> torch.Tensor:
        """高斯高通滤波器"""
        lowpass = FrequencyDomainFilters.gaussian_lowpass_filter(height, width, sigma, device)
        return 1.0 - lowpass
    
    @staticmethod
    def butterworth_lowpass_filter(height: int, width: int, cutoff: float, order: int = 2, device: torch.device = None) -> torch.Tensor:
        """
        巴特沃斯低通滤波器
        Args:
            height, width: 滤波器尺寸
            cutoff: 截止频率
            order: 滤波器阶数
            device: 设备
        Returns:
            滤波器
        """
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        filter_mask = 1.0 / (1.0 + (radial_freq / cutoff)**(2 * order))
        return filter_mask
    
    @staticmethod
    def butterworth_highpass_filter(height: int, width: int, cutoff: float, order: int = 2, device: torch.device = None) -> torch.Tensor:
        """巴特沃斯高通滤波器"""
        lowpass = FrequencyDomainFilters.butterworth_lowpass_filter(height, width, cutoff, order, device)
        return 1.0 - lowpass


class FrequencyDomainFeatures:
    """频域特征提取器"""
    
    @staticmethod
    def extract_frequency_bands(freq_domain: torch.Tensor, num_bands: int = 4) -> torch.Tensor:
        """
        提取频带特征
        Args:
            freq_domain: 频域表示
            num_bands: 频带数量
        Returns:
            频带特征 [B, num_bands, H, W]
        """
        if freq_domain.dim() == 2:
            freq_domain = freq_domain.unsqueeze(0).unsqueeze(0)
        elif freq_domain.dim() == 3:
            freq_domain = freq_domain.unsqueeze(0)
        
        batch_size, channels, height, width = freq_domain.shape
        device = freq_domain.device
        
        # 创建径向频率网格
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        
        # 计算频带边界
        max_freq = torch.max(radial_freq)
        band_boundaries = torch.linspace(0, max_freq, num_bands + 1, device=device)
        
        # 提取各频带特征
        band_features = []
        magnitude = torch.abs(freq_domain)
        
        for i in range(num_bands):
            low_freq = band_boundaries[i]
            high_freq = band_boundaries[i + 1]
            
            # 创建频带掩码
            band_mask = ((radial_freq >= low_freq) & (radial_freq < high_freq)).float()
            band_mask = band_mask.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
            
            # 提取频带特征
            band_feature = magnitude * band_mask
            band_features.append(band_feature.mean(dim=1, keepdim=True))  # 平均所有通道
        
        return torch.cat(band_features, dim=1)  # [B, num_bands, H, W]
    
    @staticmethod
    def compute_spectral_centroid(freq_domain: torch.Tensor) -> torch.Tensor:
        """
        计算频谱质心
        Args:
            freq_domain: 频域表示
        Returns:
            频谱质心坐标
        """
        magnitude = torch.abs(freq_domain)
        height, width = magnitude.shape[-2:]
        device = magnitude.device
        
        # 创建坐标网格
        y_coords = torch.arange(height, device=device, dtype=torch.float32).view(height, 1)
        x_coords = torch.arange(width, device=device, dtype=torch.float32).view(1, width)
        
        # 计算质心
        total_energy = torch.sum(magnitude, dim=(-2, -1), keepdim=True)
        centroid_y = torch.sum(magnitude * y_coords, dim=(-2, -1), keepdim=True) / (total_energy + 1e-8)
        centroid_x = torch.sum(magnitude * x_coords, dim=(-2, -1), keepdim=True) / (total_energy + 1e-8)
        
        return torch.cat([centroid_x, centroid_y], dim=-1)
    
    @staticmethod
    def compute_spectral_spread(freq_domain: torch.Tensor) -> torch.Tensor:
        """
        计算频谱扩散度
        Args:
            freq_domain: 频域表示
        Returns:
            频谱扩散度
        """
        magnitude = torch.abs(freq_domain)
        height, width = magnitude.shape[-2:]
        device = magnitude.device
        
        # 计算质心
        centroid = FrequencyDomainFeatures.compute_spectral_centroid(freq_domain)
        centroid_x, centroid_y = centroid[..., 0:1], centroid[..., 1:2]
        
        # 创建坐标网格
        y_coords = torch.arange(height, device=device, dtype=torch.float32).view(height, 1)
        x_coords = torch.arange(width, device=device, dtype=torch.float32).view(1, width)
        
        # 计算扩散度
        total_energy = torch.sum(magnitude, dim=(-2, -1), keepdim=True)
        spread_x = torch.sum(magnitude * (x_coords - centroid_x)**2, dim=(-2, -1), keepdim=True) / (total_energy + 1e-8)
        spread_y = torch.sum(magnitude * (y_coords - centroid_y)**2, dim=(-2, -1), keepdim=True) / (total_energy + 1e-8)
        
        return torch.sqrt(spread_x + spread_y)
    
    @staticmethod
    def compute_frequency_energy_ratio(freq_domain: torch.Tensor, low_freq_ratio: float = 0.1) -> torch.Tensor:
        """
        计算低频/高频能量比
        Args:
            freq_domain: 频域表示
            low_freq_ratio: 低频区域比例
        Returns:
            能量比
        """
        magnitude = torch.abs(freq_domain)
        height, width = magnitude.shape[-2:]
        device = magnitude.device
        
        # 创建径向频率网格
        radial_freq = FourierTransformUtils.create_radial_frequency_grid(height, width, device)
        max_freq = torch.max(radial_freq)
        
        # 分离低频和高频
        low_freq_mask = (radial_freq <= max_freq * low_freq_ratio).float()
        high_freq_mask = (radial_freq > max_freq * low_freq_ratio).float()
        
        # 计算能量
        low_freq_energy = torch.sum(magnitude * low_freq_mask, dim=(-2, -1), keepdim=True)
        high_freq_energy = torch.sum(magnitude * high_freq_mask, dim=(-2, -1), keepdim=True)
        
        # 计算比值
        energy_ratio = low_freq_energy / (high_freq_energy + 1e-8)
        
        return energy_ratio


class FrequencyDomainVisualizer:
    """频域可视化工具"""
    
    @staticmethod
    def plot_frequency_spectrum(image: Union[torch.Tensor, np.ndarray], title: str = "Frequency Spectrum"):
        """
        绘制频谱图
        Args:
            image: 输入图像
            title: 图像标题
        """
        if isinstance(image, torch.Tensor):
            if image.dim() > 2:
                image = image.squeeze()
            image = image.cpu().numpy()
        
        # 傅里叶变换
        f_transform = np.fft.fft2(image)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = np.log(np.abs(f_shift) + 1)
        
        # 绘制
        plt.figure(figsize=(12, 4))
        
        plt.subplot(131)
        plt.imshow(image, cmap='gray')
        plt.title('Original Image')
        plt.axis('off')
        
        plt.subplot(132)
        plt.imshow(magnitude_spectrum, cmap='gray')
        plt.title('Magnitude Spectrum')
        plt.axis('off')
        
        plt.subplot(133)
        phase_spectrum = np.angle(f_shift)
        plt.imshow(phase_spectrum, cmap='gray')
        plt.title('Phase Spectrum')
        plt.axis('off')
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_filter_response(filter_func, height: int, width: int, *args, **kwargs):
        """
        绘制滤波器响应
        Args:
            filter_func: 滤波器函数
            height, width: 滤波器尺寸
            *args, **kwargs: 滤波器参数
        """
        filter_response = filter_func(height, width, *args, **kwargs)
        
        if isinstance(filter_response, torch.Tensor):
            filter_response = filter_response.cpu().numpy()
        
        plt.figure(figsize=(8, 6))
        plt.imshow(filter_response, cmap='gray')
        plt.colorbar()
        plt.title(f'{filter_func.__name__} Response')
        plt.axis('off')
        plt.show()


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试傅里叶变换工具...")
    
    # 创建测试图像
    height, width = 64, 64
    test_image = torch.randn(1, 1, height, width)
    
    # 测试基本变换
    print("测试基本傅里叶变换...")
    freq_domain = FourierTransformUtils.fft2d(test_image)
    reconstructed = FourierTransformUtils.ifft2d(freq_domain)
    print(f"✅ 重构误差: {torch.mean((test_image - reconstructed)**2).item():.6f}")
    
    # 测试滤波器
    print("测试频域滤波器...")
    lowpass_filter = FrequencyDomainFilters.gaussian_lowpass_filter(height, width, sigma=0.1)
    highpass_filter = FrequencyDomainFilters.gaussian_highpass_filter(height, width, sigma=0.1)
    print(f"✅ 低通滤波器形状: {lowpass_filter.shape}")
    print(f"✅ 高通滤波器形状: {highpass_filter.shape}")
    
    # 测试特征提取
    print("测试频域特征提取...")
    band_features = FrequencyDomainFeatures.extract_frequency_bands(freq_domain, num_bands=4)
    centroid = FrequencyDomainFeatures.compute_spectral_centroid(freq_domain)
    spread = FrequencyDomainFeatures.compute_spectral_spread(freq_domain)
    energy_ratio = FrequencyDomainFeatures.compute_frequency_energy_ratio(freq_domain)
    
    print(f"✅ 频带特征形状: {band_features.shape}")
    print(f"✅ 频谱质心: {centroid.shape}")
    print(f"✅ 频谱扩散度: {spread.shape}")
    print(f"✅ 能量比: {energy_ratio.shape}")
    
    print("🎉 所有傅里叶变换工具测试通过！")
