#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Module Testing Script (No Unicode)
简化模块测试脚本，避免Unicode编码问题

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import torch
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fan_enhanced():
    """测试增强版FAN层"""
    print("Testing Enhanced FAN Layer...")
    try:
        from models.fan_enhanced import EnhancedFANLayer, MultiScaleFANLayer, AdaptiveFANBlock
        
        # 测试基本FAN层
        input_dim = 64
        output_dim = 128
        batch_size = 4
        
        fan_layer = EnhancedFANLayer(input_dim, output_dim)
        x = torch.randn(batch_size, input_dim)
        output = fan_layer(x)
        
        assert output.shape == (batch_size, output_dim), f"Output shape error: {output.shape}"
        
        # 测试多尺度FAN层
        multiscale_fan = MultiScaleFANLayer(input_dim, output_dim)
        output_ms = multiscale_fan(x)
        
        assert output_ms.shape == (batch_size, output_dim), f"Multi-scale output shape error: {output_ms.shape}"
        
        # 测试自适应FAN块
        adaptive_block = AdaptiveFANBlock(input_dim, 256, output_dim)
        output_adaptive = adaptive_block(x)
        
        assert output_adaptive.shape == (batch_size, output_dim), f"Adaptive block output shape error: {output_adaptive.shape}"
        
        print("PASS: Enhanced FAN Layer test passed")
        return True
        
    except Exception as e:
        print(f"FAIL: Enhanced FAN Layer test failed: {e}")
        traceback.print_exc()
        return False


def test_frequency_attention():
    """测试频域注意力机制"""
    print("Testing Frequency Attention...")
    try:
        from models.frequency_attention import (
            FrequencyChannelAttention, 
            FrequencySpatialAttention, 
            ComprehensiveFrequencyAttention
        )
        
        # 测试参数
        batch_size = 2
        channels = 64
        height, width = 32, 32
        
        x = torch.randn(batch_size, channels, height, width)
        
        # 测试通道注意力
        channel_attn = FrequencyChannelAttention(channels)
        channel_out, _ = channel_attn(x)
        
        assert channel_out.shape == x.shape, f"Channel attention output shape error: {channel_out.shape}"
        
        # 测试空间注意力
        spatial_attn = FrequencySpatialAttention(channels)
        spatial_out, _ = spatial_attn(x)
        
        assert spatial_out.shape == x.shape, f"Spatial attention output shape error: {spatial_out.shape}"
        
        # 测试综合注意力
        comprehensive_attn = ComprehensiveFrequencyAttention(channels)
        final_out, attn_info = comprehensive_attn(x)
        
        assert final_out.shape == x.shape, f"Comprehensive attention output shape error: {final_out.shape}"
        assert isinstance(attn_info, dict), "Attention info should be dict type"
        
        print("PASS: Frequency Attention test passed")
        return True
        
    except Exception as e:
        print(f"FAIL: Frequency Attention test failed: {e}")
        traceback.print_exc()
        return False


def test_fourier_infrared_net():
    """测试主检测网络"""
    print("Testing Fourier Infrared Network...")
    try:
        from models.fourier_infrared_net import FourierInfraredNet
        
        # 测试参数
        batch_size = 2
        input_channels = 1
        height, width = 128, 128
        num_classes = 1
        
        # 创建测试输入
        x = torch.randn(batch_size, input_channels, height, width)
        
        # 创建网络
        model = FourierInfraredNet(input_channels, num_classes)
        
        # 前向传播
        with torch.no_grad():
            outputs = model(x)
        
        # 检查输出
        assert 'cls_scores' in outputs, "Output should contain cls_scores"
        assert 'bbox_preds' in outputs, "Output should contain bbox_preds"
        assert 'centernesses' in outputs, "Output should contain centernesses"
        assert 'features' in outputs, "Output should contain features"
        
        assert len(outputs['cls_scores']) > 0, "cls_scores list should not be empty"
        assert len(outputs['bbox_preds']) > 0, "bbox_preds list should not be empty"
        
        print("PASS: Fourier Infrared Network test passed")
        return True
        
    except Exception as e:
        print(f"FAIL: Fourier Infrared Network test failed: {e}")
        traceback.print_exc()
        return False


def test_fourier_transforms():
    """测试傅里叶变换工具"""
    print("Testing Fourier Transform Utils...")
    try:
        from utils.fourier_transforms import (
            FourierTransformUtils, 
            FrequencyDomainFilters, 
            FrequencyDomainFeatures
        )
        
        # 创建测试图像
        height, width = 64, 64
        test_image = torch.randn(1, 1, height, width)
        
        # 测试基本变换
        freq_domain = FourierTransformUtils.fft2d(test_image)
        reconstructed = FourierTransformUtils.ifft2d(freq_domain)
        
        reconstruction_error = torch.mean((test_image - reconstructed)**2).item()
        assert reconstruction_error < 1e-5, f"Reconstruction error too large: {reconstruction_error}"
        
        # 测试滤波器
        lowpass_filter = FrequencyDomainFilters.gaussian_lowpass_filter(height, width, sigma=0.1)
        assert lowpass_filter.shape == (height, width), f"Filter shape error: {lowpass_filter.shape}"
        
        # 测试特征提取
        band_features = FrequencyDomainFeatures.extract_frequency_bands(freq_domain, num_bands=4)
        assert band_features.shape[1] == 4, f"Band features count error: {band_features.shape[1]}"
        
        print("PASS: Fourier Transform Utils test passed")
        return True
        
    except Exception as e:
        print(f"FAIL: Fourier Transform Utils test failed: {e}")
        traceback.print_exc()
        return False


def test_infrared_preprocessing():
    """测试红外图像预处理"""
    print("Testing Infrared Preprocessing...")
    try:
        from utils.infrared_preprocessing import (
            InfraredImageEnhancer, 
            NoiseReduction, 
            SmallTargetEnhancer,
            InfraredPreprocessingPipeline
        )
        
        # 创建测试图像
        test_image = torch.rand(1, 1, 128, 128)
        
        # 测试增强器
        enhancer = InfraredImageEnhancer()
        enhanced = enhancer.contrast_enhancement(test_image)
        assert enhanced.shape == test_image.shape, f"Enhanced shape error: {enhanced.shape}"
        
        # 测试去噪
        denoiser = NoiseReduction()
        denoised = denoiser.gaussian_denoising(test_image)
        assert denoised.shape == test_image.shape, f"Denoised shape error: {denoised.shape}"
        
        # 测试小目标增强
        small_enhancer = SmallTargetEnhancer()
        lap_enhanced = small_enhancer.laplacian_enhancement(test_image.squeeze())
        assert lap_enhanced.dim() >= 2, "Laplacian enhanced output dimension error"
        
        # 测试预处理流水线
        pipeline = InfraredPreprocessingPipeline()
        processed = pipeline.preprocess(test_image, training=True)
        assert processed.dim() == test_image.dim(), f"Pipeline processed dimension error: {processed.dim()} vs {test_image.dim()}"
        
        print("PASS: Infrared Preprocessing test passed")
        return True
        
    except Exception as e:
        print(f"FAIL: Infrared Preprocessing test failed: {e}")
        traceback.print_exc()
        return False


def test_config():
    """测试配置文件"""
    print("Testing Config...")
    try:
        from config import Config
        
        # 测试配置获取
        model_config = Config.get_config('model')
        assert isinstance(model_config, dict), "Model config should be dict type"
        assert 'input_channels' in model_config, "Model config should contain input_channels"
        
        train_config = Config.get_config('train')
        assert isinstance(train_config, dict), "Train config should be dict type"
        assert 'epochs' in train_config, "Train config should contain epochs"
        
        # 测试配置更新
        original_epochs = train_config['epochs']
        Config.update_config('train', {'epochs': 200})
        updated_config = Config.get_config('train')
        assert updated_config['epochs'] == 200, "Config update failed"
        
        # 恢复原始配置
        Config.update_config('train', {'epochs': original_epochs})
        
        print("PASS: Config test passed")
        return True
        
    except Exception as e:
        print(f"FAIL: Config test failed: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("Starting all module tests...")
    print("=" * 60)
    
    test_results = {}
    
    # 运行各个测试
    test_functions = [
        ('FAN Enhanced Layer', test_fan_enhanced),
        ('Frequency Attention', test_frequency_attention),
        ('Main Detection Network', test_fourier_infrared_net),
        ('Fourier Transform Utils', test_fourier_transforms),
        ('Infrared Preprocessing', test_infrared_preprocessing),
        ('Config File', test_config),
    ]
    
    for test_name, test_func in test_functions:
        print(f"\nTesting {test_name}...")
        try:
            result = test_func()
            test_results[test_name] = result
        except Exception as e:
            print(f"ERROR: {test_name} test exception: {e}")
            test_results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("SUCCESS: All tests passed! System is ready.")
        return True
    else:
        print("WARNING: Some tests failed, please check related modules.")
        return False


def main():
    """主函数"""
    print("Fourier Infrared Small Target Detection System - Module Test")
    print("=" * 60)
    print("Test Time:", torch.cuda.get_device_name(0) if torch.cuda.is_available() else "CPU Mode")
    print("Python Version:", sys.version.split()[0])
    print("PyTorch Version:", torch.__version__)
    print("CUDA Available:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("GPU Device:", torch.cuda.get_device_name(0))
        print("GPU Memory:", f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\nCongratulations! All module tests passed, system can be used normally.")
        print("\nNext steps:")
        print("  1. Prepare dataset and put it in data/ directory")
        print("  2. Run training script: python experiments/train_fourier_detector.py")
        print("  3. Run demo script: python experiments/demo.py")
    else:
        print("\nWarning: Some tests failed, please check error messages and fix related issues.")
    
    return success


if __name__ == "__main__":
    main()
