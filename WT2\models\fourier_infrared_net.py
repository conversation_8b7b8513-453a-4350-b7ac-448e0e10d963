#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fourier-based Infrared Small Target Detection Network
基于傅里叶分析的红外小目标检测网络

主要创新点：
1. 傅里叶特征提取 - 利用FAN层提取频域特征
2. 多尺度频域融合 - 融合不同尺度的频域信息
3. 频域注意力机制 - 增强重要频域特征
4. 小目标专用检测头 - 针对小目标优化的检测结构

网络架构：
- 输入: 红外图像 [B, C, H, W]
- 骨干网络: 基于FAN的特征提取器
- 颈部网络: 多尺度特征融合
- 检测头: 小目标检测头

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

from .fan_enhanced import EnhancedFANLayer, MultiScaleFANLayer, AdaptiveFANBlock
from .frequency_attention import ComprehensiveFrequencyAttention


class FourierBackbone(nn.Module):
    """
    基于傅里叶分析的骨干网络
    使用FAN层构建的特征提取器
    """
    
    def __init__(self, input_channels=3, base_channels=64):
        super(FourierBackbone, self).__init__()
        
        self.input_channels = input_channels
        self.base_channels = base_channels
        
        # 输入卷积层
        self.input_conv = nn.Sequential(
            nn.Conv2d(input_channels, base_channels, 7, stride=2, padding=3, bias=False),
            nn.BatchNorm2d(base_channels),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        )
        
        # 傅里叶特征提取阶段
        self.stage1 = self._make_stage(base_channels, base_channels, 2)
        self.stage2 = self._make_stage(base_channels, base_channels * 2, 2)
        self.stage3 = self._make_stage(base_channels * 2, base_channels * 4, 2)
        self.stage4 = self._make_stage(base_channels * 4, base_channels * 8, 2)
        
        # 频域注意力模块
        self.freq_attention1 = ComprehensiveFrequencyAttention(base_channels)
        self.freq_attention2 = ComprehensiveFrequencyAttention(base_channels * 2)
        self.freq_attention3 = ComprehensiveFrequencyAttention(base_channels * 4)
        self.freq_attention4 = ComprehensiveFrequencyAttention(base_channels * 8)
        
    def _make_stage(self, input_dim, output_dim, num_blocks):
        """构建一个阶段的网络层"""
        layers = []
        
        # 第一个块可能需要改变通道数
        if input_dim != output_dim:
            layers.append(nn.Conv2d(input_dim, output_dim, 1, bias=False))
            layers.append(nn.BatchNorm2d(output_dim))
            layers.append(nn.ReLU(inplace=True))
        
        # 添加FAN块
        for i in range(num_blocks):
            # 将2D特征图转换为1D进行FAN处理
            layers.append(FourierConvBlock(output_dim, output_dim))
        
        return nn.Sequential(*layers)
    
    def forward(self, x):
        """前向传播"""
        # 输入处理
        x = self.input_conv(x)  # [B, base_channels, H/4, W/4]
        
        # 多阶段特征提取
        c1 = self.stage1(x)  # [B, base_channels, H/4, W/4]
        c1_attn, _ = self.freq_attention1(c1)
        
        c2 = self.stage2(c1_attn)  # [B, base_channels*2, H/8, W/8]
        c2_attn, _ = self.freq_attention2(c2)
        
        c3 = self.stage3(c2_attn)  # [B, base_channels*4, H/16, W/16]
        c3_attn, _ = self.freq_attention3(c3)
        
        c4 = self.stage4(c3_attn)  # [B, base_channels*8, H/32, W/32]
        c4_attn, _ = self.freq_attention4(c4)
        
        return [c1_attn, c2_attn, c3_attn, c4_attn]


class FourierConvBlock(nn.Module):
    """
    傅里叶卷积块
    结合卷积和FAN的混合块
    """
    
    def __init__(self, channels, hidden_dim=None):
        super(FourierConvBlock, self).__init__()
        
        if hidden_dim is None:
            hidden_dim = channels * 2
        
        self.channels = channels
        self.hidden_dim = hidden_dim
        
        # 卷积分支
        self.conv_branch = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(channels)
        )
        
        # FAN分支 - 需要将2D特征转换为1D
        self.fan_branch = AdaptiveFANBlock(channels, hidden_dim, channels)
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Conv2d(channels * 2, channels, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        """前向传播"""
        batch_size, channels, height, width = x.size()
        
        # 卷积分支
        conv_out = self.conv_branch(x)
        
        # FAN分支 - 将2D特征转换为1D
        x_flat = x.view(batch_size * height * width, channels)
        fan_out = self.fan_branch(x_flat)
        fan_out = fan_out.view(batch_size, channels, height, width)
        
        # 特征融合
        fused = torch.cat([conv_out, fan_out], dim=1)
        output = self.fusion(fused)
        
        # 残差连接
        output = output + x
        
        return output


class FourierFPN(nn.Module):
    """
    基于傅里叶分析的特征金字塔网络
    """
    
    def __init__(self, in_channels_list, out_channels=256):
        super(FourierFPN, self).__init__()
        
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        
        # 横向连接
        self.lateral_convs = nn.ModuleList()
        for in_channels in in_channels_list:
            self.lateral_convs.append(
                nn.Conv2d(in_channels, out_channels, 1, bias=False)
            )
        
        # 输出卷积
        self.fpn_convs = nn.ModuleList()
        for _ in range(len(in_channels_list)):
            self.fpn_convs.append(
                nn.Conv2d(out_channels, out_channels, 3, padding=1, bias=False)
            )
        
        # 傅里叶增强模块
        self.fourier_enhancers = nn.ModuleList()
        for _ in range(len(in_channels_list)):
            self.fourier_enhancers.append(
                ComprehensiveFrequencyAttention(out_channels, reduction=8)
            )
    
    def forward(self, inputs):
        """前向传播"""
        # 构建横向连接
        laterals = []
        for i, lateral_conv in enumerate(self.lateral_convs):
            laterals.append(lateral_conv(inputs[i]))
        
        # 自顶向下路径
        for i in range(len(laterals) - 2, -1, -1):
            laterals[i] += F.interpolate(
                laterals[i + 1], 
                size=laterals[i].shape[-2:], 
                mode='bilinear', 
                align_corners=False
            )
        
        # 输出卷积和傅里叶增强
        outputs = []
        for i, (fpn_conv, fourier_enhancer) in enumerate(zip(self.fpn_convs, self.fourier_enhancers)):
            fpn_out = fpn_conv(laterals[i])
            enhanced_out, _ = fourier_enhancer(fpn_out)
            outputs.append(enhanced_out)
        
        return outputs


class SmallTargetDetectionHead(nn.Module):
    """
    小目标检测头
    专门针对小目标优化的检测结构
    """
    
    def __init__(self, in_channels, num_classes=1, num_anchors=9):
        super(SmallTargetDetectionHead, self).__init__()
        
        self.in_channels = in_channels
        self.num_classes = num_classes
        self.num_anchors = num_anchors
        
        # 分类分支
        self.cls_convs = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, num_anchors * num_classes, 3, padding=1)
        )
        
        # 回归分支
        self.reg_convs = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, num_anchors * 4, 3, padding=1)
        )
        
        # 中心度分支（用于小目标检测）
        self.centerness_conv = nn.Conv2d(in_channels, num_anchors, 3, padding=1)
        
        # 小目标增强模块
        self.small_target_enhancer = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 2, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 2, in_channels, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """前向传播"""
        # 小目标增强
        enhancement = self.small_target_enhancer(x)
        enhanced_x = x * enhancement
        
        # 分类预测
        cls_score = self.cls_convs(enhanced_x)
        
        # 回归预测
        bbox_pred = self.reg_convs(enhanced_x)
        
        # 中心度预测
        centerness = self.centerness_conv(enhanced_x)
        
        return cls_score, bbox_pred, centerness


class FourierInfraredNet(nn.Module):
    """
    完整的傅里叶红外小目标检测网络
    """
    
    def __init__(self, input_channels=3, num_classes=1, base_channels=64):
        super(FourierInfraredNet, self).__init__()
        
        self.input_channels = input_channels
        self.num_classes = num_classes
        self.base_channels = base_channels
        
        # 骨干网络
        self.backbone = FourierBackbone(input_channels, base_channels)
        
        # 特征金字塔网络
        fpn_in_channels = [base_channels, base_channels * 2, base_channels * 4, base_channels * 8]
        self.neck = FourierFPN(fpn_in_channels, out_channels=256)
        
        # 检测头
        self.detection_head = SmallTargetDetectionHead(256, num_classes)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        # 特征提取
        backbone_features = self.backbone(x)
        
        # 特征融合
        fpn_features = self.neck(backbone_features)
        
        # 多尺度检测
        all_cls_scores = []
        all_bbox_preds = []
        all_centernesses = []
        
        for fpn_feature in fpn_features:
            cls_score, bbox_pred, centerness = self.detection_head(fpn_feature)
            all_cls_scores.append(cls_score)
            all_bbox_preds.append(bbox_pred)
            all_centernesses.append(centerness)
        
        return {
            'cls_scores': all_cls_scores,
            'bbox_preds': all_bbox_preds,
            'centernesses': all_centernesses,
            'features': fpn_features
        }


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试傅里叶红外检测网络...")
    
    # 测试参数
    batch_size = 2
    input_channels = 3
    height, width = 256, 256
    num_classes = 1
    
    # 创建测试输入
    x = torch.randn(batch_size, input_channels, height, width)
    
    # 创建网络
    model = FourierInfraredNet(input_channels, num_classes)
    
    # 前向传播
    with torch.no_grad():
        outputs = model(x)
    
    print(f"✅ 网络测试通过")
    print(f"   输入形状: {x.shape}")
    print(f"   分类输出数量: {len(outputs['cls_scores'])}")
    print(f"   回归输出数量: {len(outputs['bbox_preds'])}")
    print(f"   中心度输出数量: {len(outputs['centernesses'])}")
    print(f"   特征图数量: {len(outputs['features'])}")
    
    # 打印各层输出形状
    for i, (cls, bbox, center, feat) in enumerate(zip(
        outputs['cls_scores'], 
        outputs['bbox_preds'], 
        outputs['centernesses'],
        outputs['features']
    )):
        print(f"   第{i+1}层 - 分类: {cls.shape}, 回归: {bbox.shape}, 中心度: {center.shape}, 特征: {feat.shape}")
    
    # 计算参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"📊 网络参数统计:")
    print(f"   总参数量: {total_params:,}")
    print(f"   可训练参数量: {trainable_params:,}")
    
    print("🎉 傅里叶红外检测网络测试完成！")
