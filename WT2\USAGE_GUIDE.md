# 使用指南 - 基于傅里叶分析的红外小目标检测网络

## 🎯 概述

本指南将详细介绍如何通过手动输入路径来配置和使用傅里叶红外小目标检测系统。

## 🚀 快速开始

### 方法一：一键启动（最简单）
```bash
cd WT2
python quick_start.py
```
系统将自动引导您完成所有配置。

### 方法二：分步操作（更灵活）

#### 1. 系统检查
```bash
python test_modules.py
```
确保所有模块正常工作。

#### 2. 数据集配置
```bash
python setup_dataset.py
```
交互式配置数据集路径。

#### 3. 模型训练
```bash
python experiments/train_fourier_detector.py --interactive
```

#### 4. 模型演示
```bash
python experiments/demo.py --interactive
```

## 📁 数据集路径配置

### 支持的路径格式

#### Windows路径
```bash
# 绝对路径
"D:\Dataset\infrared_images"
"C:\Users\<USER>\Documents\dataset"

# 使用正斜杠
"D:/Dataset/infrared_images"

# 网络路径
"\\server\share\dataset"
```

#### 相对路径
```bash
# 相对于项目目录
"./data"
"../parent_folder/dataset"
"../../shared_data/infrared"
```

### 数据集结构

#### 标准结构（推荐）
```
your_dataset/
├── train/
│   ├── images/          # 训练图像 (.png, .jpg, .jpeg)
│   └── labels/          # 训练标签 (.json, .txt, .xml)
├── val/
│   ├── images/          # 验证图像
│   └── labels/          # 验证标签
└── test/
    ├── images/          # 测试图像
    └── labels/          # 测试标签
```

#### 简单结构（自动识别）
```
simple_dataset/
├── image1.png
├── image1.json
├── image2.jpg
├── image2.json
└── ...
```

## 🎯 训练模型

### 交互式训练（推荐）
```bash
python experiments/train_fourier_detector.py --interactive
```
系统会引导您：
1. 选择数据集路径
2. 配置训练参数
3. 开始训练

### 命令行训练
```bash
# 基础训练
python experiments/train_fourier_detector.py --data_root "D:\MyDataset"

# 完整参数
python experiments/train_fourier_detector.py \
    --data_root "D:\MyDataset" \
    --batch_size 8 \
    --epochs 100 \
    --lr 1e-4 \
    --device cuda

# 快速测试
python experiments/train_fourier_detector.py \
    --data_root "./data" \
    --epochs 10 \
    --batch_size 4
```

### 训练参数说明
- `--data_root`: 数据集根目录路径
- `--batch_size`: 批大小（默认8，内存不足时可减小到4）
- `--epochs`: 训练轮数（默认100）
- `--lr`: 学习率（默认1e-4）
- `--device`: 设备选择（auto/cpu/cuda）
- `--save_dir`: 模型保存目录

## 🎨 模型演示

### 交互式演示（推荐）
```bash
python experiments/demo.py --interactive
```
系统会引导您：
1. 选择模型文件
2. 选择图像路径
3. 查看检测结果

### 命令行演示
```bash
# 单张图像
python experiments/demo.py \
    --model_path "checkpoints/best_model.pth" \
    --image_path "D:\TestImages\sample.png"

# 批量处理
python experiments/demo.py \
    --model_path "checkpoints/best_model.pth" \
    --image_path "D:\TestImages\"

# 使用War Thunder图像
python experiments/demo.py \
    --image_path "../data/WT/output_images1.png"
```

### 演示参数说明
- `--model_path`: 训练好的模型路径
- `--image_path`: 图像文件或文件夹路径
- `--save_dir`: 结果保存目录
- `--conf_threshold`: 置信度阈值（默认0.5）

## 🔧 常见问题解决

### 路径相关问题

#### 问题：路径包含空格或特殊字符
```bash
# 解决：使用双引号包围路径
python experiments/train_fourier_detector.py --data_root "D:\My Dataset\infrared images"
```

#### 问题：路径包含中文
```bash
# 解决：使用英文路径，或确保系统支持UTF-8
# 推荐使用英文路径
```

#### 问题：找不到数据集
```bash
# 解决：检查路径是否正确
# 1. 使用绝对路径
# 2. 检查文件夹是否存在
# 3. 检查权限
```

### 内存相关问题

#### 问题：GPU内存不足
```bash
# 解决：减小批大小
python experiments/train_fourier_detector.py --data_root "./data" --batch_size 4

# 或使用CPU
python experiments/train_fourier_detector.py --data_root "./data" --device cpu
```

#### 问题：系统内存不足
```bash
# 解决：减少数据加载线程
# 修改config.py中的num_workers参数
```

### 模型相关问题

#### 问题：模型文件不存在
```bash
# 解决：先进行训练
python experiments/train_fourier_detector.py --interactive

# 或使用随机初始化模型（仅用于测试）
python experiments/demo.py --interactive
```

#### 问题：CUDA错误
```bash
# 解决：使用CPU模式
python experiments/train_fourier_detector.py --device cpu
python experiments/demo.py --device cpu
```

## 📊 结果查看

### 训练结果
- 模型文件：`checkpoints/`
- 训练日志：`results/`
- 损失曲线：`checkpoints/loss_curves.png`

### 演示结果
- 检测结果：`demo_results/`
- 可视化图像：包含检测框和置信度
- 频域分析：频谱图和特征分析

## 💡 使用技巧

### 1. 路径输入技巧
- Windows用户可以直接拖拽文件夹到命令行获取路径
- 使用Tab键自动补全路径
- 复制粘贴路径时注意去除多余的引号

### 2. 性能优化
- GPU训练：确保CUDA可用，使用适当的batch_size
- CPU训练：减小batch_size，增加num_workers
- 内存优化：关闭不必要的程序，使用pin_memory=False

### 3. 调试技巧
- 使用小数据集快速测试：`--epochs 5 --batch_size 2`
- 查看详细日志：检查终端输出和日志文件
- 逐步验证：先测试模块，再测试训练，最后测试演示

## 📞 获取帮助

### 查看帮助信息
```bash
python experiments/train_fourier_detector.py --help
python experiments/demo.py --help
```

### 运行示例
```bash
python example_usage.py
```

### 查看配置
```bash
python config.py
```

## 🎉 完整示例

以下是一个完整的使用示例：

```bash
# 1. 进入项目目录
cd WT2

# 2. 检查系统
python test_modules.py

# 3. 配置数据集（交互式）
python setup_dataset.py

# 4. 训练模型（假设数据集在D:\MyDataset）
python experiments/train_fourier_detector.py --data_root "D:\MyDataset" --epochs 50

# 5. 演示模型（假设测试图像在D:\TestImages\sample.png）
python experiments/demo.py --image_path "D:\TestImages\sample.png"

# 6. 查看结果
# 检查 demo_results/ 文件夹中的结果
```

---

**🎊 恭喜！您已经掌握了傅里叶红外小目标检测系统的使用方法！**
