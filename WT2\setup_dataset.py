#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dataset Setup Script for Fourier Infrared Detection
数据集配置脚本，帮助用户设置数据集路径和结构

主要功能：
1. 交互式数据集路径配置
2. 数据集结构检查和创建
3. 数据集格式验证
4. 配置文件生成

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import json
import glob
import shutil
from typing import Dict, List, Optional
from pathlib import Path


class DatasetSetup:
    """数据集配置类"""
    
    def __init__(self):
        self.config_file = "dataset_config.json"
        self.supported_image_formats = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
        self.supported_label_formats = ['.json', '.txt', '.xml']
    
    def welcome(self):
        """显示欢迎信息"""
        print("🗂️ 傅里叶红外小目标检测 - 数据集配置工具")
        print("=" * 60)
        print("本工具将帮助您配置数据集路径和结构")
        print("支持的图像格式:", ", ".join(self.supported_image_formats))
        print("支持的标签格式:", ", ".join(self.supported_label_formats))
        print("=" * 60)
    
    def detect_existing_datasets(self) -> List[str]:
        """检测现有的数据集"""
        print("\n🔍 正在搜索现有数据集...")
        
        datasets = []
        search_paths = [
            ".",
            "..",
            "../..",
            "../data",
            "../../data",
            "../data/WT",
            "../../data/WT",
            "data",
            "dataset",
            "datasets"
        ]
        
        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue
                
            # 检查是否包含图像文件
            image_count = 0
            for ext in self.supported_image_formats:
                pattern = os.path.join(search_path, f"**/*{ext}")
                image_count += len(glob.glob(pattern, recursive=True))
            
            if image_count > 0:
                abs_path = os.path.abspath(search_path)
                if abs_path not in datasets:
                    datasets.append(abs_path)
                    print(f"  ✅ 找到数据集: {abs_path} ({image_count} 张图像)")
        
        return datasets
    
    def get_dataset_path(self) -> str:
        """获取数据集路径"""
        existing_datasets = self.detect_existing_datasets()
        
        while True:
            print("\n📁 请选择数据集配置方式:")
            print("1. 手动输入数据集路径")
            print("2. 从检测到的数据集中选择")
            print("3. 创建新的数据集结构")
            print("4. 使用War Thunder数据集")
            
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                return self._manual_input_path()
            elif choice == '2':
                return self._select_from_existing(existing_datasets)
            elif choice == '3':
                return self._create_new_dataset()
            elif choice == '4':
                return self._setup_wt_dataset()
            else:
                print("❌ 无效选择，请重新输入")
    
    def _manual_input_path(self) -> str:
        """手动输入路径"""
        while True:
            path = input("\n请输入数据集根目录路径: ").strip().strip('"\'')
            if not path:
                print("❌ 路径不能为空")
                continue
            
            if os.path.exists(path):
                print(f"✅ 路径确认: {path}")
                return path
            else:
                create = input(f"路径不存在，是否创建? (y/n): ").strip().lower()
                if create in ['y', 'yes']:
                    try:
                        os.makedirs(path, exist_ok=True)
                        print(f"✅ 创建路径: {path}")
                        return path
                    except Exception as e:
                        print(f"❌ 创建路径失败: {e}")
                        continue
                else:
                    continue
    
    def _select_from_existing(self, datasets: List[str]) -> str:
        """从现有数据集中选择"""
        if not datasets:
            print("❌ 未找到现有数据集，请选择其他方式")
            return self.get_dataset_path()
        
        print(f"\n找到 {len(datasets)} 个数据集:")
        for i, dataset in enumerate(datasets, 1):
            print(f"  {i}. {dataset}")
        
        while True:
            try:
                idx = int(input(f"\n请选择数据集 (1-{len(datasets)}): ")) - 1
                if 0 <= idx < len(datasets):
                    selected = datasets[idx]
                    print(f"✅ 选择数据集: {selected}")
                    return selected
                else:
                    print("❌ 选择无效")
            except ValueError:
                print("❌ 请输入有效数字")
    
    def _create_new_dataset(self) -> str:
        """创建新的数据集结构"""
        base_path = input("\n请输入新数据集的基础路径: ").strip().strip('"\'')
        if not base_path:
            base_path = "./new_dataset"
        
        dataset_name = input("请输入数据集名称 (默认: infrared_dataset): ").strip()
        if not dataset_name:
            dataset_name = "infrared_dataset"
        
        dataset_path = os.path.join(base_path, dataset_name)
        
        # 创建标准数据集结构
        structure = {
            'train': ['images', 'labels'],
            'val': ['images', 'labels'],
            'test': ['images', 'labels']
        }
        
        try:
            for split, subdirs in structure.items():
                for subdir in subdirs:
                    dir_path = os.path.join(dataset_path, split, subdir)
                    os.makedirs(dir_path, exist_ok=True)
            
            print(f"✅ 创建数据集结构: {dataset_path}")
            print("📁 数据集结构:")
            print(f"  {dataset_path}/")
            print("  ├── train/")
            print("  │   ├── images/")
            print("  │   └── labels/")
            print("  ├── val/")
            print("  │   ├── images/")
            print("  │   └── labels/")
            print("  └── test/")
            print("      ├── images/")
            print("      └── labels/")
            
            return dataset_path
            
        except Exception as e:
            print(f"❌ 创建数据集结构失败: {e}")
            return self.get_dataset_path()
    
    def _setup_wt_dataset(self) -> str:
        """设置War Thunder数据集"""
        print("\n🎮 War Thunder数据集配置")
        
        # 搜索WT数据集
        wt_paths = [
            "../data/WT",
            "../../data/WT", 
            "../../../data/WT",
            "data/WT",
            "WT"
        ]
        
        found_wt = None
        for wt_path in wt_paths:
            if os.path.exists(wt_path):
                # 检查是否包含WT数据集文件
                if any(os.path.exists(os.path.join(wt_path, f)) for f in ['infrared_detector.py', 'README.md']):
                    found_wt = os.path.abspath(wt_path)
                    break
        
        if found_wt:
            print(f"✅ 找到War Thunder数据集: {found_wt}")
            use_wt = input("是否使用此数据集? (y/n): ").strip().lower()
            if use_wt in ['y', 'yes']:
                return found_wt
        
        # 手动输入WT数据集路径
        wt_path = input("\n请输入War Thunder数据集路径: ").strip().strip('"\'')
        if wt_path and os.path.exists(wt_path):
            return wt_path
        else:
            print("❌ War Thunder数据集路径无效")
            return self.get_dataset_path()
    
    def analyze_dataset(self, dataset_path: str) -> Dict:
        """分析数据集结构"""
        print(f"\n📊 分析数据集: {dataset_path}")
        
        analysis = {
            'path': dataset_path,
            'total_images': 0,
            'total_labels': 0,
            'splits': {},
            'image_formats': {},
            'label_formats': {}
        }
        
        # 分析图像文件
        for ext in self.supported_image_formats:
            pattern = os.path.join(dataset_path, f"**/*{ext}")
            files = glob.glob(pattern, recursive=True)
            if files:
                analysis['image_formats'][ext] = len(files)
                analysis['total_images'] += len(files)
        
        # 分析标签文件
        for ext in self.supported_label_formats:
            pattern = os.path.join(dataset_path, f"**/*{ext}")
            files = glob.glob(pattern, recursive=True)
            if files:
                analysis['label_formats'][ext] = len(files)
                analysis['total_labels'] += len(files)
        
        # 分析数据集分割
        for split in ['train', 'val', 'test']:
            split_path = os.path.join(dataset_path, split)
            if os.path.exists(split_path):
                split_images = 0
                split_labels = 0
                
                for ext in self.supported_image_formats:
                    pattern = os.path.join(split_path, f"**/*{ext}")
                    split_images += len(glob.glob(pattern, recursive=True))
                
                for ext in self.supported_label_formats:
                    pattern = os.path.join(split_path, f"**/*{ext}")
                    split_labels += len(glob.glob(pattern, recursive=True))
                
                analysis['splits'][split] = {
                    'images': split_images,
                    'labels': split_labels
                }
        
        return analysis
    
    def display_analysis(self, analysis: Dict):
        """显示分析结果"""
        print("\n📈 数据集分析结果:")
        print("=" * 40)
        print(f"📁 路径: {analysis['path']}")
        print(f"🖼️ 总图像数: {analysis['total_images']}")
        print(f"🏷️ 总标签数: {analysis['total_labels']}")
        
        if analysis['image_formats']:
            print("\n📊 图像格式分布:")
            for fmt, count in analysis['image_formats'].items():
                print(f"  {fmt}: {count}")
        
        if analysis['label_formats']:
            print("\n📊 标签格式分布:")
            for fmt, count in analysis['label_formats'].items():
                print(f"  {fmt}: {count}")
        
        if analysis['splits']:
            print("\n📊 数据集分割:")
            for split, data in analysis['splits'].items():
                print(f"  {split}: {data['images']} 图像, {data['labels']} 标签")
    
    def save_config(self, dataset_path: str, analysis: Dict):
        """保存配置"""
        config = {
            'dataset_path': dataset_path,
            'analysis': analysis,
            'created_at': str(Path().absolute()),
            'version': '1.0'
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"\n💾 配置已保存: {self.config_file}")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def run(self):
        """运行配置流程"""
        self.welcome()
        
        # 获取数据集路径
        dataset_path = self.get_dataset_path()
        
        # 分析数据集
        analysis = self.analyze_dataset(dataset_path)
        
        # 显示分析结果
        self.display_analysis(analysis)
        
        # 保存配置
        save_config = input("\n💾 是否保存配置? (y/n): ").strip().lower()
        if save_config in ['y', 'yes']:
            self.save_config(dataset_path, analysis)
        
        print("\n🎉 数据集配置完成!")
        print(f"📁 数据集路径: {dataset_path}")
        print("\n📖 接下来您可以:")
        print("  1. 运行训练: python experiments/train_fourier_detector.py --data_root", dataset_path)
        print("  2. 运行演示: python experiments/demo.py --interactive")
        
        return dataset_path


def main():
    """主函数"""
    try:
        setup = DatasetSetup()
        dataset_path = setup.run()
        return dataset_path
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出配置")
    except Exception as e:
        print(f"\n❌ 配置过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
