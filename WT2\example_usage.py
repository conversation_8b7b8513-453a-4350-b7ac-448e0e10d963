#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example Usage Script for Fourier Infrared Detection
使用示例脚本，展示如何手动配置数据集路径并运行系统

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys


def example_manual_dataset_setup():
    """示例：手动设置数据集路径"""
    print("📖 示例：手动设置数据集路径")
    print("=" * 50)
    
    # 示例数据集路径（请根据实际情况修改）
    example_paths = [
        r"D:\Dataset\infrared_images",           # Windows绝对路径
        r"C:\Users\<USER>\Documents\dataset", # Windows用户目录
        "./data",                                # 相对路径
        "../parent_folder/dataset",              # 父目录
        "/home/<USER>/dataset",                    # Linux路径
        "~/Documents/dataset"                    # 用户主目录
    ]
    
    print("常见的数据集路径格式：")
    for i, path in enumerate(example_paths, 1):
        print(f"  {i}. {path}")
    
    print("\n💡 使用技巧：")
    print("1. Windows路径可以使用 \\ 或 / 作为分隔符")
    print("2. 路径中有空格时，请用引号包围")
    print("3. 支持相对路径和绝对路径")
    print("4. 可以直接拖拽文件夹到命令行获取路径")


def example_training_commands():
    """示例：训练命令"""
    print("\n🎯 示例：训练命令")
    print("=" * 50)
    
    commands = [
        # 交互式训练（推荐）
        "python experiments/train_fourier_detector.py --interactive",
        
        # 手动指定路径
        'python experiments/train_fourier_detector.py --data_root "D:\\MyDataset"',
        
        # 使用相对路径
        "python experiments/train_fourier_detector.py --data_root ./data",
        
        # 完整参数
        'python experiments/train_fourier_detector.py --data_root "D:\\Dataset" --batch_size 8 --epochs 100 --lr 1e-4',
        
        # 快速测试
        "python experiments/train_fourier_detector.py --data_root ./data --epochs 10 --batch_size 4"
    ]
    
    print("常用训练命令：")
    for i, cmd in enumerate(commands, 1):
        print(f"\n{i}. {cmd}")
        if i == 1:
            print("   💡 推荐：交互式选择数据集路径")
        elif i == 2:
            print("   💡 直接指定Windows路径")
        elif i == 3:
            print("   💡 使用项目内的data文件夹")
        elif i == 4:
            print("   💡 完整参数配置")
        elif i == 5:
            print("   💡 快速测试（少量epoch）")


def example_demo_commands():
    """示例：演示命令"""
    print("\n🎨 示例：演示命令")
    print("=" * 50)
    
    commands = [
        # 交互式演示（推荐）
        "python experiments/demo.py --interactive",
        
        # 单张图像
        'python experiments/demo.py --image_path "D:\\TestImages\\sample.png"',
        
        # 批量图像
        'python experiments/demo.py --image_path "D:\\TestImages\\"',
        
        # 指定模型和图像
        'python experiments/demo.py --model_path checkpoints/best_model.pth --image_path "test.png"',
        
        # 使用War Thunder图像
        'python experiments/demo.py --image_path "../data/WT/output_images1.png"'
    ]
    
    print("常用演示命令：")
    for i, cmd in enumerate(commands, 1):
        print(f"\n{i}. {cmd}")
        if i == 1:
            print("   💡 推荐：交互式选择图像")
        elif i == 2:
            print("   💡 处理单张图像")
        elif i == 3:
            print("   💡 批量处理文件夹中的图像")
        elif i == 4:
            print("   💡 指定特定模型")
        elif i == 5:
            print("   💡 使用WT数据集图像")


def example_step_by_step():
    """示例：完整的使用流程"""
    print("\n🚀 示例：完整使用流程")
    print("=" * 50)
    
    steps = [
        ("1. 系统测试", "python test_modules.py"),
        ("2. 数据集配置", "python setup_dataset.py"),
        ("3. 模型训练", "python experiments/train_fourier_detector.py --interactive"),
        ("4. 模型演示", "python experiments/demo.py --interactive"),
        ("5. 查看结果", "# 查看 results/ 和 demo_results/ 文件夹")
    ]
    
    print("完整使用流程：")
    for step, command in steps:
        print(f"\n{step}")
        print(f"   命令: {command}")
    
    print("\n💡 或者使用一键启动：")
    print("   python quick_start.py")


def example_common_issues():
    """示例：常见问题解决"""
    print("\n🔧 常见问题解决")
    print("=" * 50)
    
    issues = [
        {
            "问题": "路径包含中文或特殊字符",
            "解决": "使用英文路径，或将路径用双引号包围"
        },
        {
            "问题": "找不到数据集",
            "解决": "检查路径是否正确，使用绝对路径"
        },
        {
            "问题": "内存不足",
            "解决": "减小batch_size，如 --batch_size 4"
        },
        {
            "问题": "CUDA错误",
            "解决": "使用CPU模式：--device cpu"
        },
        {
            "问题": "模型文件不存在",
            "解决": "先运行训练，或使用 --interactive 模式"
        }
    ]
    
    for issue in issues:
        print(f"\n❓ {issue['问题']}")
        print(f"   💡 {issue['解决']}")


def main():
    """主函数"""
    print("📚 傅里叶红外小目标检测系统 - 使用示例")
    print("=" * 60)
    print("本脚本展示了如何手动配置数据集路径并使用系统")
    print("=" * 60)
    
    # 显示各种示例
    example_manual_dataset_setup()
    example_training_commands()
    example_demo_commands()
    example_step_by_step()
    example_common_issues()
    
    print("\n🎉 示例展示完成！")
    print("\n📖 更多信息请查看：")
    print("   - README.md: 完整的项目说明")
    print("   - config.py: 详细的配置选项")
    print("   - PROJECT_SUMMARY.md: 项目总结")
    
    print("\n🚀 快速开始：")
    print("   python quick_start.py")


if __name__ == "__main__":
    main()
