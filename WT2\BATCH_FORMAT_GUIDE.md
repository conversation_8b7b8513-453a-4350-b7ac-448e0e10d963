# Batch格式数据集使用指南

## 📋 概述

本指南专门针对您的batch格式数据集，文件命名格式如下：
- `batch5_22080717046_1.json` / `batch5_22080717046_1.png`
- `batch5_22080717046_2.json` / `batch5_22080717046_2.png`
- `batch6_22080717047_1.json` / `batch6_22080717047_1.png`
- ...

## 📁 支持的文件格式

### 图像文件
- `batch*.png` （推荐）
- `batch*.jpg`
- `batch*.jpeg`

### 标签文件
- `batch*.json`

## 🗂️ 目录结构

### 方式一：简单结构（推荐）
```
your_dataset/
├── batch5_22080717046_1.png
├── batch5_22080717046_1.json
├── batch5_22080717046_2.png
├── batch5_22080717046_2.json
├── batch6_22080717047_1.png
├── batch6_22080717047_1.json
└── ...
```

### 方式二：分类结构
```
your_dataset/
├── train/
│   ├── batch5_22080717046_1.png
│   ├── batch5_22080717046_1.json
│   └── ...
├── val/
│   ├── batch6_22080717047_1.png
│   ├── batch6_22080717047_1.json
│   └── ...
└── test/
    ├── batch7_22080717048_1.png
    ├── batch7_22080717048_1.json
    └── ...
```

## 🚀 快速开始

### 1. 配置数据集
```bash
# 进入项目目录
cd WT2

# 运行batch数据集配置工具
python batch_dataset_setup.py
```

配置工具将：
- 🔍 自动搜索batch格式文件
- 📊 分析数据集统计信息
- 💾 保存配置文件

### 2. 训练模型

#### 方法一：交互式训练（推荐）
```bash
python experiments/train_fourier_detector.py --interactive
```

#### 方法二：直接指定路径
```bash
# Windows路径示例
python experiments/train_fourier_detector.py --data_root "D:\MyBatchDataset"

# 相对路径示例
python experiments/train_fourier_detector.py --data_root "./batch_data"

# 完整参数示例
python experiments/train_fourier_detector.py \
    --data_root "D:\MyBatchDataset" \
    --batch_size 8 \
    --epochs 100 \
    --lr 1e-4
```

### 3. 模型演示

#### 方法一：交互式演示（推荐）
```bash
python experiments/demo.py --interactive
```

#### 方法二：直接指定图像
```bash
# 单张图像
python experiments/demo.py \
    --image_path "D:\MyBatchDataset\batch5_22080717046_1.png"

# 批量处理整个文件夹
python experiments/demo.py \
    --image_path "D:\MyBatchDataset\" \
    --save_dir results
```

## 🔧 数据集自动分割

系统会自动将您的batch数据集分割为训练/验证/测试集：
- **训练集**: 70% 的数据
- **验证集**: 10% 的数据  
- **测试集**: 20% 的数据

分割基于文件名的哈希值，确保：
- ✅ 分割结果稳定一致
- ✅ 同一batch的文件在同一分割中
- ✅ 数据分布均匀

## 📊 JSON标签格式

系统支持多种JSON标签格式，推荐格式如下：

```json
{
  "image_name": "batch5_22080717046_1.png",
  "image_size": {
    "width": 1920,
    "height": 1080
  },
  "targets": [
    {
      "id": 1,
      "class": "small_target",
      "bbox": {
        "x": 100,
        "y": 200,
        "width": 20,
        "height": 15
      },
      "confidence": 1.0,
      "visible": true
    }
  ]
}
```

### 兼容格式
系统也兼容其他常见格式：
- COCO格式
- YOLO格式转换的JSON
- 自定义格式

## 🛠️ 实用工具

### 1. 数据集分析工具
```bash
python batch_example.py
```
选择"4. 分析数据集"，输入您的数据集路径。

### 2. 格式示例查看
```bash
python batch_example.py
```
选择相应选项查看：
- 文件格式信息
- 目录结构示例
- JSON格式示例

### 3. 一键启动
```bash
python quick_start.py
```
自动引导完成所有配置和使用。

## 📝 使用示例

### 完整使用流程
```bash
# 1. 进入项目目录
cd WT2

# 2. 配置batch数据集
python batch_dataset_setup.py
# 选择您的batch数据集路径，如: D:\MyBatchDataset

# 3. 训练模型
python experiments/train_fourier_detector.py \
    --data_root "D:\MyBatchDataset" \
    --epochs 50 \
    --batch_size 8

# 4. 演示模型
python experiments/demo.py \
    --image_path "D:\MyBatchDataset\batch5_22080717046_1.png" \
    --save_dir results

# 5. 查看结果
# 检查 results/ 文件夹中的检测结果
```

## ⚠️ 注意事项

### 1. 路径格式
- Windows路径使用双引号包围：`"D:\MyBatchDataset"`
- 支持正斜杠：`"D:/MyBatchDataset"`
- 相对路径：`"./batch_data"`

### 2. 文件匹配
- 确保每个图像文件都有对应的JSON标签文件
- 文件名必须完全匹配（除了扩展名）
- 系统会自动检查匹配情况

### 3. 性能优化
- 如果GPU内存不足，减小`--batch_size`参数
- 如果训练时间过长，减少`--epochs`参数
- 使用`--device cpu`强制使用CPU模式

## 🔍 故障排除

### 问题1：找不到数据集
```
解决方案：
1. 检查路径是否正确
2. 确保路径中包含batch格式文件
3. 使用绝对路径而非相对路径
```

### 问题2：文件匹配率低
```
解决方案：
1. 检查文件命名是否一致
2. 确保图像和标签文件在同一目录
3. 运行 python batch_example.py 分析数据集
```

### 问题3：训练内存不足
```
解决方案：
1. 减小batch_size: --batch_size 4
2. 使用CPU模式: --device cpu
3. 关闭其他程序释放内存
```

## 📞 获取帮助

### 查看帮助信息
```bash
python experiments/train_fourier_detector.py --help
python experiments/demo.py --help
```

### 运行示例和测试
```bash
python batch_example.py          # 查看使用示例
python test_modules_simple.py    # 测试系统功能
python quick_start.py            # 一键启动向导
```

---

**🎉 现在您可以开始使用batch格式数据集进行红外小目标检测了！**
