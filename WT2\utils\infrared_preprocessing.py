#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Infrared Image Preprocessing for Small Target Detection
红外图像预处理工具，专门用于小目标检测

主要功能：
1. 红外图像增强 - 对比度增强、直方图均衡化
2. 噪声抑制 - 各种去噪算法
3. 小目标增强 - 专门针对小目标的增强方法
4. 数据增强 - 训练时的数据增强策略

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from typing import Tuple, Optional, Union, List
import random
from PIL import Image, ImageEnhance


class InfraredImageEnhancer:
    """红外图像增强器"""
    
    @staticmethod
    def adaptive_histogram_equalization(image: torch.Tensor, clip_limit: float = 2.0, tile_grid_size: Tuple[int, int] = (8, 8)) -> torch.Tensor:
        """
        自适应直方图均衡化
        Args:
            image: 输入图像 [B, C, H, W] 或 [H, W]
            clip_limit: 对比度限制
            tile_grid_size: 瓦片网格大小
        Returns:
            增强后的图像
        """
        if isinstance(image, torch.Tensor):
            was_tensor = True
            original_shape = image.shape
            if image.dim() == 4:
                batch_size, channels = image.shape[:2]
                image = image.view(-1, *image.shape[2:])
            elif image.dim() == 3:
                batch_size, channels = 1, image.shape[0]
                image = image.view(-1, *image.shape[1:])
            else:
                batch_size, channels = 1, 1
                image = image.unsqueeze(0)
            
            # 转换为numpy进行CLAHE处理
            enhanced_images = []
            for i in range(image.shape[0]):
                img_np = (image[i].cpu().numpy() * 255).astype(np.uint8)
                clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
                enhanced_np = clahe.apply(img_np)
                enhanced_tensor = torch.from_numpy(enhanced_np.astype(np.float32) / 255.0)
                enhanced_images.append(enhanced_tensor)
            
            result = torch.stack(enhanced_images, dim=0)
            return result.view(original_shape).to(image.device)
        else:
            # numpy数组处理
            img_uint8 = (image * 255).astype(np.uint8)
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
            enhanced = clahe.apply(img_uint8)
            return enhanced.astype(np.float32) / 255.0
    
    @staticmethod
    def contrast_enhancement(image: torch.Tensor, alpha: float = 1.5, beta: float = 0.0) -> torch.Tensor:
        """
        对比度增强
        Args:
            image: 输入图像
            alpha: 对比度系数
            beta: 亮度偏移
        Returns:
            增强后的图像
        """
        enhanced = alpha * image + beta
        return torch.clamp(enhanced, 0.0, 1.0)
    
    @staticmethod
    def gamma_correction(image: torch.Tensor, gamma: float = 0.8) -> torch.Tensor:
        """
        伽马校正
        Args:
            image: 输入图像
            gamma: 伽马值
        Returns:
            校正后的图像
        """
        return torch.pow(image + 1e-8, gamma)
    
    @staticmethod
    def top_hat_transform(image: torch.Tensor, kernel_size: int = 15) -> torch.Tensor:
        """
        顶帽变换，用于增强小目标
        Args:
            image: 输入图像
            kernel_size: 结构元素大小
        Returns:
            变换后的图像
        """
        if isinstance(image, torch.Tensor):
            # 转换为numpy进行形态学操作
            was_tensor = True
            device = image.device
            original_shape = image.shape
            
            if image.dim() > 2:
                image_np = image.squeeze().cpu().numpy()
            else:
                image_np = image.cpu().numpy()
            
            # 创建结构元素
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            
            # 顶帽变换
            if image_np.dtype != np.uint8:
                image_np = (image_np * 255).astype(np.uint8)
            
            tophat = cv2.morphologyEx(image_np, cv2.MORPH_TOPHAT, kernel)
            result = torch.from_numpy(tophat.astype(np.float32) / 255.0).to(device)
            
            return result.view(original_shape)
        else:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            tophat = cv2.morphologyEx(image, cv2.MORPH_TOPHAT, kernel)
            return tophat.astype(np.float32) / 255.0
    
    @staticmethod
    def unsharp_masking(image: torch.Tensor, sigma: float = 1.0, strength: float = 1.5) -> torch.Tensor:
        """
        非锐化掩模增强
        Args:
            image: 输入图像
            sigma: 高斯模糊标准差
            strength: 增强强度
        Returns:
            增强后的图像
        """
        # 高斯模糊 - 使用自定义实现以兼容旧版本PyTorch
        kernel_size = int(6 * sigma + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 创建高斯核
        coords = torch.arange(kernel_size, dtype=torch.float32, device=image.device)
        coords -= kernel_size // 2
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g /= g.sum()

        # 应用1D高斯滤波两次（分离卷积）
        if image.dim() == 2:
            image = image.unsqueeze(0).unsqueeze(0)
        elif image.dim() == 3:
            image = image.unsqueeze(0)

        # 水平方向
        g_h = g.view(1, 1, 1, -1).expand(image.size(1), 1, 1, -1)
        blurred = F.conv2d(image, g_h, padding=(0, kernel_size//2), groups=image.size(1))

        # 垂直方向
        g_v = g.view(1, 1, -1, 1).expand(image.size(1), 1, -1, 1)
        blurred = F.conv2d(blurred, g_v, padding=(kernel_size//2, 0), groups=image.size(1))

        blurred = blurred.squeeze()
        image = image.squeeze()

        # 非锐化掩模
        enhanced = image + strength * (image - blurred)

        return torch.clamp(enhanced, 0.0, 1.0)


class NoiseReduction:
    """噪声抑制器"""
    
    @staticmethod
    def bilateral_filter(image: torch.Tensor, d: int = 9, sigma_color: float = 75, sigma_space: float = 75) -> torch.Tensor:
        """
        双边滤波
        Args:
            image: 输入图像
            d: 滤波器直径
            sigma_color: 颜色空间标准差
            sigma_space: 坐标空间标准差
        Returns:
            滤波后的图像
        """
        if isinstance(image, torch.Tensor):
            device = image.device
            original_shape = image.shape
            
            if image.dim() > 2:
                image_np = image.squeeze().cpu().numpy()
            else:
                image_np = image.cpu().numpy()
            
            if image_np.dtype != np.uint8:
                image_np = (image_np * 255).astype(np.uint8)
            
            filtered = cv2.bilateralFilter(image_np, d, sigma_color, sigma_space)
            result = torch.from_numpy(filtered.astype(np.float32) / 255.0).to(device)
            
            return result.view(original_shape)
        else:
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            filtered = cv2.bilateralFilter(image, d, sigma_color, sigma_space)
            return filtered.astype(np.float32) / 255.0
    
    @staticmethod
    def non_local_means_denoising(image: torch.Tensor, h: float = 10, template_window_size: int = 7, search_window_size: int = 21) -> torch.Tensor:
        """
        非局部均值去噪
        Args:
            image: 输入图像
            h: 滤波强度
            template_window_size: 模板窗口大小
            search_window_size: 搜索窗口大小
        Returns:
            去噪后的图像
        """
        if isinstance(image, torch.Tensor):
            device = image.device
            original_shape = image.shape
            
            if image.dim() > 2:
                image_np = image.squeeze().cpu().numpy()
            else:
                image_np = image.cpu().numpy()
            
            if image_np.dtype != np.uint8:
                image_np = (image_np * 255).astype(np.uint8)
            
            denoised = cv2.fastNlMeansDenoising(image_np, None, h, template_window_size, search_window_size)
            result = torch.from_numpy(denoised.astype(np.float32) / 255.0).to(device)
            
            return result.view(original_shape)
        else:
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            denoised = cv2.fastNlMeansDenoising(image, None, h, template_window_size, search_window_size)
            return denoised.astype(np.float32) / 255.0
    
    @staticmethod
    def gaussian_denoising(image: torch.Tensor, sigma: float = 1.0) -> torch.Tensor:
        """
        高斯去噪
        Args:
            image: 输入图像
            sigma: 高斯标准差
        Returns:
            去噪后的图像
        """
        kernel_size = int(6 * sigma + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 创建高斯核
        coords = torch.arange(kernel_size, dtype=torch.float32, device=image.device)
        coords -= kernel_size // 2
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g /= g.sum()

        # 应用1D高斯滤波两次（分离卷积）
        if image.dim() == 2:
            image = image.unsqueeze(0).unsqueeze(0)
        elif image.dim() == 3:
            image = image.unsqueeze(0)

        original_shape = image.shape

        # 水平方向
        g_h = g.view(1, 1, 1, -1).expand(image.size(1), 1, 1, -1)
        blurred = F.conv2d(image, g_h, padding=(0, kernel_size//2), groups=image.size(1))

        # 垂直方向
        g_v = g.view(1, 1, -1, 1).expand(image.size(1), 1, -1, 1)
        blurred = F.conv2d(blurred, g_v, padding=(kernel_size//2, 0), groups=image.size(1))

        # 恢复原始形状
        if len(original_shape) == 2:
            blurred = blurred.squeeze()
        elif len(original_shape) == 3:
            blurred = blurred.squeeze(0)
        elif len(original_shape) == 4:
            # 保持4D形状
            pass

        return blurred
    
    @staticmethod
    def median_filter(image: torch.Tensor, kernel_size: int = 5) -> torch.Tensor:
        """
        中值滤波
        Args:
            image: 输入图像
            kernel_size: 核大小
        Returns:
            滤波后的图像
        """
        if isinstance(image, torch.Tensor):
            device = image.device
            original_shape = image.shape
            
            if image.dim() > 2:
                image_np = image.squeeze().cpu().numpy()
            else:
                image_np = image.cpu().numpy()
            
            if image_np.dtype != np.uint8:
                image_np = (image_np * 255).astype(np.uint8)
            
            filtered = cv2.medianBlur(image_np, kernel_size)
            result = torch.from_numpy(filtered.astype(np.float32) / 255.0).to(device)
            
            return result.view(original_shape)
        else:
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            filtered = cv2.medianBlur(image, kernel_size)
            return filtered.astype(np.float32) / 255.0


class SmallTargetEnhancer:
    """小目标增强器"""
    
    @staticmethod
    def laplacian_enhancement(image: torch.Tensor, strength: float = 0.5) -> torch.Tensor:
        """
        拉普拉斯增强
        Args:
            image: 输入图像
            strength: 增强强度
        Returns:
            增强后的图像
        """
        # 拉普拉斯核
        laplacian_kernel = torch.tensor([[[
            [0, -1, 0],
            [-1, 4, -1],
            [0, -1, 0]
        ]]], dtype=image.dtype, device=image.device)
        
        if image.dim() == 2:
            image = image.unsqueeze(0).unsqueeze(0)
        elif image.dim() == 3:
            image = image.unsqueeze(0)
        
        # 应用拉普拉斯滤波
        laplacian = F.conv2d(image, laplacian_kernel, padding=1)
        
        # 增强
        enhanced = image + strength * laplacian
        
        return torch.clamp(enhanced, 0.0, 1.0).squeeze()
    
    @staticmethod
    def high_boost_filtering(image: torch.Tensor, boost_factor: float = 2.0, sigma: float = 1.0) -> torch.Tensor:
        """
        高频增强滤波
        Args:
            image: 输入图像
            boost_factor: 增强因子
            sigma: 高斯模糊标准差
        Returns:
            增强后的图像
        """
        # 使用高斯去噪函数进行低通滤波
        lowpass = NoiseReduction.gaussian_denoising(image, sigma)

        # 高频增强
        enhanced = boost_factor * image - lowpass

        return torch.clamp(enhanced, 0.0, 1.0)
    
    @staticmethod
    def multi_scale_enhancement(image: torch.Tensor, scales: List[float] = [0.5, 1.0, 2.0]) -> torch.Tensor:
        """
        多尺度增强
        Args:
            image: 输入图像
            scales: 尺度列表
        Returns:
            增强后的图像
        """
        enhanced_images = []
        
        for scale in scales:
            if scale != 1.0:
                # 缩放图像
                if scale < 1.0:
                    # 下采样
                    scaled = F.interpolate(image.unsqueeze(0).unsqueeze(0), scale_factor=scale, mode='bilinear', align_corners=False)
                    # 上采样回原尺寸
                    scaled = F.interpolate(scaled, size=image.shape[-2:], mode='bilinear', align_corners=False)
                else:
                    # 上采样
                    scaled = F.interpolate(image.unsqueeze(0).unsqueeze(0), scale_factor=scale, mode='bilinear', align_corners=False)
                    # 下采样回原尺寸
                    scaled = F.interpolate(scaled, size=image.shape[-2:], mode='bilinear', align_corners=False)
                
                enhanced_images.append(scaled.squeeze())
            else:
                enhanced_images.append(image)
        
        # 融合多尺度结果
        enhanced = torch.stack(enhanced_images, dim=0).mean(dim=0)
        
        return enhanced


class InfraredDataAugmentation:
    """红外图像数据增强"""
    
    @staticmethod
    def random_brightness_contrast(image: torch.Tensor, brightness_range: Tuple[float, float] = (0.8, 1.2), 
                                 contrast_range: Tuple[float, float] = (0.8, 1.2)) -> torch.Tensor:
        """随机亮度对比度调整"""
        brightness = random.uniform(*brightness_range)
        contrast = random.uniform(*contrast_range)
        
        enhanced = contrast * image + (brightness - 1.0)
        return torch.clamp(enhanced, 0.0, 1.0)
    
    @staticmethod
    def random_noise(image: torch.Tensor, noise_type: str = 'gaussian', intensity: float = 0.05) -> torch.Tensor:
        """
        随机噪声添加
        Args:
            image: 输入图像
            noise_type: 噪声类型 ('gaussian', 'salt_pepper', 'poisson')
            intensity: 噪声强度
        Returns:
            添加噪声后的图像
        """
        if noise_type == 'gaussian':
            noise = torch.randn_like(image) * intensity
            noisy = image + noise
        elif noise_type == 'salt_pepper':
            noise = torch.rand_like(image)
            salt = (noise > 1 - intensity/2).float()
            pepper = (noise < intensity/2).float()
            noisy = image * (1 - salt - pepper) + salt
        elif noise_type == 'poisson':
            # 泊松噪声近似
            scaled = image * 255
            noisy_scaled = torch.poisson(scaled) / 255
            noisy = noisy_scaled
        else:
            noisy = image
        
        return torch.clamp(noisy, 0.0, 1.0)
    
    @staticmethod
    def random_blur(image: torch.Tensor, blur_range: Tuple[float, float] = (0.5, 2.0)) -> torch.Tensor:
        """随机模糊"""
        sigma = random.uniform(*blur_range)
        return NoiseReduction.gaussian_denoising(image, sigma)


class InfraredPreprocessingPipeline:
    """红外图像预处理流水线"""
    
    def __init__(self, config: dict = None):
        """
        初始化预处理流水线
        Args:
            config: 配置字典
        """
        self.config = config or self._default_config()
        
        self.enhancer = InfraredImageEnhancer()
        self.denoiser = NoiseReduction()
        self.small_target_enhancer = SmallTargetEnhancer()
        self.augmenter = InfraredDataAugmentation()
    
    def _default_config(self) -> dict:
        """默认配置"""
        return {
            'enhance_contrast': True,
            'gamma_correction': True,
            'gamma_value': 0.8,
            'denoise': True,
            'denoise_method': 'bilateral',
            'enhance_small_targets': True,
            'augment_training': True
        }
    
    def preprocess(self, image: torch.Tensor, training: bool = False) -> torch.Tensor:
        """
        预处理图像
        Args:
            image: 输入图像
            training: 是否为训练模式
        Returns:
            预处理后的图像
        """
        original_shape = image.shape
        processed = image.clone()

        # 对比度增强
        if self.config.get('enhance_contrast', True):
            processed = self.enhancer.contrast_enhancement(processed, alpha=1.2)

        # 伽马校正
        if self.config.get('gamma_correction', True):
            gamma = self.config.get('gamma_value', 0.8)
            processed = self.enhancer.gamma_correction(processed, gamma)

        # 去噪
        if self.config.get('denoise', True):
            method = self.config.get('denoise_method', 'bilateral')
            if method == 'bilateral':
                processed = self.denoiser.bilateral_filter(processed)
            elif method == 'gaussian':
                processed = self.denoiser.gaussian_denoising(processed)

        # 小目标增强
        if self.config.get('enhance_small_targets', True):
            # 确保输入是正确的形状
            if processed.dim() == 4:
                processed_2d = processed.squeeze()
            else:
                processed_2d = processed
            enhanced = self.small_target_enhancer.laplacian_enhancement(processed_2d)
            # 恢复原始形状
            if enhanced.dim() < len(original_shape):
                for _ in range(len(original_shape) - enhanced.dim()):
                    enhanced = enhanced.unsqueeze(0)
            processed = enhanced

        # 训练时数据增强
        if training and self.config.get('augment_training', True):
            if random.random() < 0.5:
                processed = self.augmenter.random_brightness_contrast(processed)
            if random.random() < 0.3:
                processed = self.augmenter.random_noise(processed, intensity=0.02)

        # 确保输出形状与输入一致
        if processed.shape != original_shape:
            # 尝试调整形状
            if processed.numel() == torch.tensor(original_shape).prod():
                processed = processed.view(original_shape)

        return processed


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试红外图像预处理工具...")
    
    # 创建测试图像
    test_image = torch.rand(1, 1, 128, 128)
    
    # 测试增强器
    print("测试图像增强...")
    enhancer = InfraredImageEnhancer()
    enhanced = enhancer.contrast_enhancement(test_image)
    gamma_corrected = enhancer.gamma_correction(test_image)
    print(f"✅ 对比度增强: {enhanced.shape}")
    print(f"✅ 伽马校正: {gamma_corrected.shape}")
    
    # 测试去噪
    print("测试噪声抑制...")
    denoiser = NoiseReduction()
    denoised = denoiser.gaussian_denoising(test_image)
    print(f"✅ 高斯去噪: {denoised.shape}")
    
    # 测试小目标增强
    print("测试小目标增强...")
    small_enhancer = SmallTargetEnhancer()
    lap_enhanced = small_enhancer.laplacian_enhancement(test_image.squeeze())
    print(f"✅ 拉普拉斯增强: {lap_enhanced.shape}")
    
    # 测试预处理流水线
    print("测试预处理流水线...")
    pipeline = InfraredPreprocessingPipeline()
    processed = pipeline.preprocess(test_image, training=True)
    print(f"✅ 流水线处理: {processed.shape}")
    
    print("🎉 所有红外图像预处理工具测试通过！")
