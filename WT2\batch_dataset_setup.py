#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch Dataset Setup for Fourier Infrared Detection
专门针对batch格式数据集的配置脚本

支持的文件格式：
- batch5_22080717046_1.json / batch5_22080717046_1.png
- batch5_22080717046_2.json / batch5_22080717046_2.png
- ...

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import json
import glob
import shutil
from typing import Dict, List, Optional
from pathlib import Path


class BatchDatasetSetup:
    """Batch格式数据集配置类"""
    
    def __init__(self):
        self.config_file = "batch_dataset_config.json"
        self.supported_image_formats = ['.png', '.jpg', '.jpeg']
        self.supported_label_formats = ['.json']
    
    def welcome(self):
        """显示欢迎信息"""
        print("Batch格式数据集配置工具")
        print("=" * 50)
        print("支持的文件格式:")
        print("  图像: batch*.png, batch*.jpg")
        print("  标签: batch*.json")
        print("=" * 50)
    
    def detect_batch_datasets(self) -> List[str]:
        """检测batch格式数据集"""
        print("\n搜索batch格式数据集...")
        
        datasets = []
        search_paths = [
            ".",
            "..",
            "../..",
            "../data",
            "../../data",
            "data",
            "dataset",
            "datasets"
        ]
        
        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue
            
            # 查找batch格式文件
            batch_images = glob.glob(os.path.join(search_path, "**/batch*.png"), recursive=True)
            batch_images.extend(glob.glob(os.path.join(search_path, "**/batch*.jpg"), recursive=True))
            
            if batch_images:
                # 获取目录路径
                dataset_dirs = set()
                for img_path in batch_images:
                    dataset_dirs.add(os.path.dirname(img_path))
                
                for dataset_dir in dataset_dirs:
                    abs_path = os.path.abspath(dataset_dir)
                    if abs_path not in datasets:
                        # 统计文件数量
                        img_count = len(glob.glob(os.path.join(dataset_dir, "batch*.png")))
                        img_count += len(glob.glob(os.path.join(dataset_dir, "batch*.jpg")))
                        json_count = len(glob.glob(os.path.join(dataset_dir, "batch*.json")))
                        
                        datasets.append(abs_path)
                        print(f"  找到数据集: {abs_path}")
                        print(f"    图像文件: {img_count}, 标签文件: {json_count}")
        
        return datasets
    
    def get_dataset_path(self) -> str:
        """获取数据集路径"""
        existing_datasets = self.detect_batch_datasets()
        
        while True:
            print("\n请选择数据集配置方式:")
            print("1. 手动输入batch数据集路径")
            print("2. 从检测到的数据集中选择")
            print("3. 浏览文件夹选择")
            
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == '1':
                return self._manual_input_path()
            elif choice == '2':
                return self._select_from_existing(existing_datasets)
            elif choice == '3':
                return self._browse_folder()
            else:
                print("无效选择，请重新输入")
    
    def _manual_input_path(self) -> str:
        """手动输入路径"""
        while True:
            print("\n请输入batch数据集目录路径:")
            print("示例: D:\\MyDataset\\batch_data")
            path = input("路径: ").strip().strip('"\'')
            
            if not path:
                print("路径不能为空")
                continue
            
            if os.path.exists(path):
                # 验证是否包含batch格式文件
                batch_files = glob.glob(os.path.join(path, "batch*.png"))
                batch_files.extend(glob.glob(os.path.join(path, "batch*.jpg")))
                
                if batch_files:
                    print(f"路径确认: {path}")
                    print(f"找到 {len(batch_files)} 个batch图像文件")
                    return path
                else:
                    print("该目录中未找到batch格式文件")
                    continue
            else:
                print(f"路径不存在: {path}")
                continue
    
    def _select_from_existing(self, datasets: List[str]) -> str:
        """从现有数据集中选择"""
        if not datasets:
            print("未找到batch格式数据集，请选择其他方式")
            return self.get_dataset_path()
        
        print(f"\n找到 {len(datasets)} 个batch数据集:")
        for i, dataset in enumerate(datasets, 1):
            print(f"  {i}. {dataset}")
        
        while True:
            try:
                idx = int(input(f"\n请选择数据集 (1-{len(datasets)}): ")) - 1
                if 0 <= idx < len(datasets):
                    selected = datasets[idx]
                    print(f"选择数据集: {selected}")
                    return selected
                else:
                    print("选择无效")
            except ValueError:
                print("请输入有效数字")
    
    def _browse_folder(self) -> str:
        """浏览文件夹"""
        print("\n请手动输入要浏览的根目录:")
        root_path = input("根目录 (默认当前目录): ").strip() or "."
        
        if not os.path.exists(root_path):
            print("目录不存在")
            return self.get_dataset_path()
        
        # 搜索该目录下的batch文件
        batch_dirs = []
        for root, dirs, files in os.walk(root_path):
            batch_files = [f for f in files if f.startswith('batch') and f.endswith(('.png', '.jpg'))]
            if batch_files:
                batch_dirs.append(root)
        
        if not batch_dirs:
            print("未找到包含batch文件的目录")
            return self.get_dataset_path()
        
        print(f"\n找到 {len(batch_dirs)} 个包含batch文件的目录:")
        for i, dir_path in enumerate(batch_dirs, 1):
            file_count = len(glob.glob(os.path.join(dir_path, "batch*")))
            print(f"  {i}. {dir_path} ({file_count} 个文件)")
        
        while True:
            try:
                idx = int(input(f"\n请选择目录 (1-{len(batch_dirs)}): ")) - 1
                if 0 <= idx < len(batch_dirs):
                    selected = batch_dirs[idx]
                    print(f"选择目录: {selected}")
                    return selected
                else:
                    print("选择无效")
            except ValueError:
                print("请输入有效数字")
    
    def analyze_batch_dataset(self, dataset_path: str) -> Dict:
        """分析batch数据集"""
        print(f"\n分析batch数据集: {dataset_path}")
        
        analysis = {
            'path': dataset_path,
            'total_images': 0,
            'total_labels': 0,
            'matched_pairs': 0,
            'batch_prefixes': set(),
            'file_samples': []
        }
        
        # 查找所有batch文件
        image_files = []
        for ext in ['.png', '.jpg', '.jpeg']:
            pattern = os.path.join(dataset_path, f"batch*{ext}")
            image_files.extend(glob.glob(pattern))
        
        label_files = glob.glob(os.path.join(dataset_path, "batch*.json"))
        
        analysis['total_images'] = len(image_files)
        analysis['total_labels'] = len(label_files)
        
        # 分析匹配的图像-标签对
        matched_pairs = 0
        for img_file in image_files:
            base_name = os.path.splitext(os.path.basename(img_file))[0]
            label_file = os.path.join(dataset_path, base_name + '.json')
            
            if os.path.exists(label_file):
                matched_pairs += 1
                
                # 提取batch前缀
                if base_name.startswith('batch'):
                    parts = base_name.split('_')
                    if len(parts) >= 2:
                        batch_prefix = parts[0]  # 如 'batch5'
                        analysis['batch_prefixes'].add(batch_prefix)
        
        analysis['matched_pairs'] = matched_pairs
        analysis['batch_prefixes'] = list(analysis['batch_prefixes'])
        
        # 收集文件样本
        sample_files = image_files[:5]  # 前5个文件作为样本
        for img_file in sample_files:
            base_name = os.path.splitext(os.path.basename(img_file))[0]
            label_file = os.path.join(dataset_path, base_name + '.json')
            analysis['file_samples'].append({
                'image': os.path.basename(img_file),
                'label': os.path.basename(label_file) if os.path.exists(label_file) else 'Missing',
                'matched': os.path.exists(label_file)
            })
        
        return analysis
    
    def display_analysis(self, analysis: Dict):
        """显示分析结果"""
        print("\nbatch数据集分析结果:")
        print("=" * 40)
        print(f"路径: {analysis['path']}")
        print(f"总图像数: {analysis['total_images']}")
        print(f"总标签数: {analysis['total_labels']}")
        print(f"匹配对数: {analysis['matched_pairs']}")
        print(f"匹配率: {analysis['matched_pairs']/analysis['total_images']*100:.1f}%" if analysis['total_images'] > 0 else "匹配率: 0%")
        
        if analysis['batch_prefixes']:
            print(f"Batch前缀: {', '.join(analysis['batch_prefixes'])}")
        
        if analysis['file_samples']:
            print("\n文件样本:")
            for sample in analysis['file_samples']:
                status = "✓" if sample['matched'] else "✗"
                print(f"  {status} {sample['image']} -> {sample['label']}")
    
    def save_config(self, dataset_path: str, analysis: Dict):
        """保存配置"""
        config = {
            'dataset_path': dataset_path,
            'dataset_type': 'batch_format',
            'analysis': analysis,
            'created_at': str(Path().absolute()),
            'version': '1.0'
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"\n配置已保存: {self.config_file}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def run(self):
        """运行配置流程"""
        self.welcome()
        
        # 获取数据集路径
        dataset_path = self.get_dataset_path()
        
        # 分析数据集
        analysis = self.analyze_batch_dataset(dataset_path)
        
        # 显示分析结果
        self.display_analysis(analysis)
        
        # 保存配置
        save_config = input("\n是否保存配置? (y/n): ").strip().lower()
        if save_config in ['y', 'yes']:
            self.save_config(dataset_path, analysis)
        
        print("\nbatch数据集配置完成!")
        print(f"数据集路径: {dataset_path}")
        print("\n接下来您可以:")
        print(f"  1. 运行训练: python experiments/train_fourier_detector.py --data_root \"{dataset_path}\"")
        print(f"  2. 运行演示: python experiments/demo.py --interactive")
        
        return dataset_path


def main():
    """主函数"""
    try:
        setup = BatchDatasetSetup()
        dataset_path = setup.run()
        return dataset_path
    except KeyboardInterrupt:
        print("\n\n用户中断，退出配置")
    except Exception as e:
        print(f"\n配置过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
