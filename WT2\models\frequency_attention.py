#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Frequency Domain Attention Mechanisms for Infrared Small Target Detection
频域注意力机制，专门用于红外小目标检测

主要创新点：
1. 频域通道注意力 - 在频域分析通道重要性
2. 频域空间注意力 - 在频域分析空间位置重要性
3. 多频带注意力 - 分析不同频带的重要性
4. 自适应频域滤波 - 根据输入自适应调整频域滤波器

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math


class FrequencyChannelAttention(nn.Module):
    """
    频域通道注意力机制
    在频域分析不同通道的重要性
    """
    
    def __init__(self, channels, reduction=16):
        super(FrequencyChannelAttention, self).__init__()
        
        self.channels = channels
        self.reduction = reduction
        
        # 频域特征提取器
        self.freq_extractor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        # 频域权重生成器
        self.freq_weight_gen = nn.Sequential(
            nn.Linear(channels, channels // reduction),
            nn.ReLU(),
            nn.Linear(channels // reduction, channels),
            nn.Sigmoid()
        )
        
        # 频带分析器
        self.freq_bands = nn.Parameter(torch.Tensor(4, channels))  # 4个频带
        self._initialize_parameters()
    
    def _initialize_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.freq_bands)
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入特征图 [batch_size, channels, height, width]
        Returns:
            注意力增强后的特征图
        """
        batch_size, channels, height, width = x.size()
        
        # 1. 频域变换
        x_fft = torch.fft.fft2(x, dim=(-2, -1))
        x_magnitude = torch.abs(x_fft)
        x_phase = torch.angle(x_fft)
        
        # 2. 频域特征提取
        freq_features = self.freq_extractor(x_magnitude)
        freq_features = freq_features.view(batch_size, channels)
        
        # 3. 频带分析
        freq_band_weights = []
        for i in range(4):  # 4个频带
            band_weight = torch.sum(freq_features * self.freq_bands[i], dim=1, keepdim=True)
            freq_band_weights.append(band_weight)
        
        freq_band_weights = torch.cat(freq_band_weights, dim=1)  # [batch_size, 4]
        freq_band_weights = F.softmax(freq_band_weights, dim=1)
        
        # 4. 生成通道注意力权重
        channel_weights = self.freq_weight_gen(freq_features)
        channel_weights = channel_weights.view(batch_size, channels, 1, 1)
        
        # 5. 应用注意力权重
        enhanced_magnitude = x_magnitude * channel_weights
        enhanced_fft = enhanced_magnitude * torch.exp(1j * x_phase)
        
        # 6. 逆变换回空域
        enhanced_x = torch.fft.ifft2(enhanced_fft, dim=(-2, -1)).real
        
        return enhanced_x, freq_band_weights


class FrequencySpatialAttention(nn.Module):
    """
    频域空间注意力机制
    在频域分析空间位置的重要性
    """
    
    def __init__(self, channels):
        super(FrequencySpatialAttention, self).__init__()
        
        self.channels = channels
        
        # 空间频域分析器
        self.spatial_freq_analyzer = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, padding=3, bias=False),
            nn.Sigmoid()
        )
        
        # 频域空间权重生成器
        self.spatial_weight_gen = nn.Sequential(
            nn.Conv2d(channels, channels // 8, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 8, 1, 1),
            nn.Sigmoid()
        )
        
        # 多尺度频域卷积
        self.multiscale_freq_conv = nn.ModuleList([
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.Conv2d(channels, channels, kernel_size=5, padding=2)
        ])
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入特征图 [batch_size, channels, height, width]
        Returns:
            空间注意力增强后的特征图
        """
        batch_size, channels, height, width = x.size()
        
        # 1. 频域变换
        x_fft = torch.fft.fft2(x, dim=(-2, -1))
        x_magnitude = torch.abs(x_fft)
        x_phase = torch.angle(x_fft)
        
        # 2. 计算频域空间统计
        freq_mean = torch.mean(x_magnitude, dim=1, keepdim=True)
        freq_max, _ = torch.max(x_magnitude, dim=1, keepdim=True)
        freq_spatial_stats = torch.cat([freq_mean, freq_max], dim=1)
        
        # 3. 生成空间注意力权重
        spatial_attention = self.spatial_freq_analyzer(freq_spatial_stats)
        
        # 4. 多尺度频域处理
        multiscale_features = []
        for conv in self.multiscale_freq_conv:
            scale_feature = conv(x_magnitude)
            multiscale_features.append(scale_feature)
        
        # 融合多尺度特征
        fused_magnitude = sum(multiscale_features) / len(multiscale_features)
        
        # 5. 应用空间注意力
        enhanced_magnitude = fused_magnitude * spatial_attention
        enhanced_fft = enhanced_magnitude * torch.exp(1j * x_phase)
        
        # 6. 逆变换回空域
        enhanced_x = torch.fft.ifft2(enhanced_fft, dim=(-2, -1)).real
        
        return enhanced_x, spatial_attention


class AdaptiveFrequencyFilter(nn.Module):
    """
    简化的自适应频域滤波器
    """

    def __init__(self, channels):
        super(AdaptiveFrequencyFilter, self).__init__()

        self.channels = channels

        # 简化的滤波器参数生成网络
        self.filter_gen = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 4, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 4, 1, 1),  # 生成一个滤波强度参数
            nn.Sigmoid()
        )

    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入特征图 [batch_size, channels, height, width]
        Returns:
            滤波后的特征图
        """
        # 1. 生成滤波强度
        filter_strength = self.filter_gen(x)  # [batch_size, 1, 1, 1]

        # 2. 频域变换
        x_fft = torch.fft.fft2(x, dim=(-2, -1))

        # 3. 简单的低通滤波（保留低频，抑制高频）
        batch_size, channels, height, width = x.size()

        # 创建简单的低通滤波器
        center_h, center_w = height // 2, width // 2
        y, x_coord = torch.meshgrid(torch.arange(height), torch.arange(width), indexing='ij')
        y, x_coord = y.to(x.device), x_coord.to(x.device)

        # 计算到中心的距离
        dist = torch.sqrt((y - center_h)**2 + (x_coord - center_w)**2)

        # 创建高斯低通滤波器
        sigma = 0.3 * min(height, width)  # 固定的滤波器大小
        lowpass_filter = torch.exp(-0.5 * (dist / sigma)**2)
        lowpass_filter = lowpass_filter.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]

        # 4. 应用滤波器
        filtered_fft = x_fft * lowpass_filter
        filtered_x = torch.fft.ifft2(filtered_fft, dim=(-2, -1)).real

        # 5. 根据滤波强度混合原始和滤波后的信号
        result = filter_strength * filtered_x + (1 - filter_strength) * x

        return result


class ComprehensiveFrequencyAttention(nn.Module):
    """
    综合频域注意力机制
    结合通道注意力、空间注意力和自适应滤波的完整模块
    """
    
    def __init__(self, channels, reduction=16):
        super(ComprehensiveFrequencyAttention, self).__init__()
        
        self.channels = channels
        
        # 各个注意力模块
        self.channel_attention = FrequencyChannelAttention(channels, reduction)
        self.spatial_attention = FrequencySpatialAttention(channels)
        self.adaptive_filter = AdaptiveFrequencyFilter(channels)
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(channels * 3, channels, 1),
            nn.BatchNorm2d(channels),
            nn.ReLU(),
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.BatchNorm2d(channels)
        )
        
        # 输出权重
        self.output_weights = nn.Parameter(torch.ones(3) / 3)
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入特征图 [batch_size, channels, height, width]
        Returns:
            综合注意力增强后的特征图和注意力权重
        """
        # 1. 通道注意力
        channel_enhanced, freq_band_weights = self.channel_attention(x)
        
        # 2. 空间注意力
        spatial_enhanced, spatial_weights = self.spatial_attention(x)
        
        # 3. 自适应滤波
        filtered_x = self.adaptive_filter(x)
        
        # 4. 特征融合
        # 方法1: 加权融合
        weights = F.softmax(self.output_weights, dim=0)
        weighted_fusion = (channel_enhanced * weights[0] + 
                          spatial_enhanced * weights[1] + 
                          filtered_x * weights[2])
        
        # 方法2: 卷积融合
        concat_features = torch.cat([channel_enhanced, spatial_enhanced, filtered_x], dim=1)
        conv_fusion = self.feature_fusion(concat_features)
        
        # 5. 最终输出
        final_output = weighted_fusion + conv_fusion + x  # 残差连接
        
        attention_info = {
            'freq_band_weights': freq_band_weights,
            'spatial_weights': spatial_weights,
            'fusion_weights': weights
        }
        
        return final_output, attention_info


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试频域注意力机制...")
    
    # 测试参数
    batch_size = 4
    channels = 64
    height, width = 32, 32
    
    x = torch.randn(batch_size, channels, height, width)
    
    # 测试通道注意力
    print("测试频域通道注意力...")
    channel_attn = FrequencyChannelAttention(channels)
    channel_out, freq_weights = channel_attn(x)
    print(f"✅ 通道注意力测试通过: {channel_out.shape}")
    
    # 测试空间注意力
    print("测试频域空间注意力...")
    spatial_attn = FrequencySpatialAttention(channels)
    spatial_out, spatial_weights = spatial_attn(x)
    print(f"✅ 空间注意力测试通过: {spatial_out.shape}")
    
    # 测试自适应滤波器
    print("测试自适应频域滤波器...")
    adaptive_filter = AdaptiveFrequencyFilter(channels)
    filtered_out = adaptive_filter(x)
    print(f"✅ 自适应滤波器测试通过: {filtered_out.shape}")
    
    # 测试综合注意力
    print("测试综合频域注意力...")
    comprehensive_attn = ComprehensiveFrequencyAttention(channels)
    final_out, attn_info = comprehensive_attn(x)
    print(f"✅ 综合注意力测试通过: {final_out.shape}")
    print(f"   注意力信息键: {list(attn_info.keys())}")
    
    print("🎉 所有频域注意力机制测试通过！")
