#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Training Script for Fourier-based Infrared Small Target Detection Network
基于傅里叶分析的红外小目标检测网络训练脚本

主要功能：
1. 数据加载和预处理
2. 模型训练和验证
3. 损失函数和优化器配置
4. 训练过程监控和可视化
5. 模型保存和恢复

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Optional

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F

import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import cv2
from PIL import Image

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.fourier_infrared_net import FourierInfraredNet
from utils.infrared_preprocessing import InfraredPreprocessingPipeline
from utils.fourier_transforms import FourierTransformUtils


class InfraredTargetDataset(Dataset):
    """红外小目标数据集"""
    
    def __init__(self, data_root: str, split: str = 'train', transform=None, target_transform=None):
        """
        初始化数据集
        Args:
            data_root: 数据根目录
            split: 数据集分割 ('train', 'val', 'test')
            transform: 图像变换
            target_transform: 标签变换
        """
        self.data_root = data_root
        self.split = split
        self.transform = transform
        self.target_transform = target_transform
        
        # 加载数据列表
        self.data_list = self._load_data_list()
        
        # 预处理流水线
        self.preprocessor = InfraredPreprocessingPipeline()
        
        print(f"📊 {split}数据集加载完成: {len(self.data_list)}个样本")
    
    def _load_data_list(self) -> List[Dict]:
        """加载数据列表"""
        data_list = []
        
        # 查找图像和标签文件
        image_dir = os.path.join(self.data_root, 'images', self.split)
        label_dir = os.path.join(self.data_root, 'labels', self.split)
        
        if not os.path.exists(image_dir):
            print(f"⚠️ 图像目录不存在: {image_dir}")
            return data_list
        
        # 遍历图像文件
        for img_file in os.listdir(image_dir):
            if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                img_path = os.path.join(image_dir, img_file)
                
                # 查找对应的标签文件
                base_name = os.path.splitext(img_file)[0]
                label_file = base_name + '.json'
                label_path = os.path.join(label_dir, label_file)
                
                if os.path.exists(label_path):
                    data_list.append({
                        'image_path': img_path,
                        'label_path': label_path,
                        'image_name': img_file
                    })
        
        return data_list
    
    def _load_image(self, image_path: str) -> torch.Tensor:
        """加载图像"""
        try:
            # 使用PIL加载图像
            image = Image.open(image_path).convert('L')  # 转为灰度图
            image_array = np.array(image, dtype=np.float32) / 255.0
            
            # 转换为tensor
            image_tensor = torch.from_numpy(image_array).unsqueeze(0)  # [1, H, W]
            
            return image_tensor
        except Exception as e:
            print(f"❌ 图像加载失败 {image_path}: {e}")
            return torch.zeros(1, 256, 256)  # 返回默认图像
    
    def _load_labels(self, label_path: str) -> Dict:
        """加载标签"""
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
            
            # 提取目标框信息
            targets = []
            if 'flightModels' in label_data:
                for target in label_data['flightModels']:
                    if target.get('isvisible', False):
                        bbox = target.get('bbox', {})
                        if all(k in bbox for k in ['x', 'y', 'width', 'height']):
                            targets.append({
                                'bbox': [bbox['x'], bbox['y'], bbox['width'], bbox['height']],
                                'class': 0,  # 假设只有一个类别
                                'name': target.get('name', 'unknown')
                            })
            
            return {'targets': targets}
        except Exception as e:
            print(f"❌ 标签加载失败 {label_path}: {e}")
            return {'targets': []}
    
    def __len__(self) -> int:
        return len(self.data_list)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, Dict]:
        """获取数据项"""
        data_item = self.data_list[idx]
        
        # 加载图像
        image = self._load_image(data_item['image_path'])
        
        # 加载标签
        labels = self._load_labels(data_item['label_path'])
        
        # 预处理
        if self.split == 'train':
            image = self.preprocessor.preprocess(image, training=True)
        else:
            image = self.preprocessor.preprocess(image, training=False)
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        if self.target_transform:
            labels = self.target_transform(labels)
        
        return image, labels


class FocalLoss(nn.Module):
    """Focal Loss for object detection"""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算Focal Loss
        Args:
            inputs: 预测结果 [N, C]
            targets: 真实标签 [N]
        Returns:
            损失值
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class DetectionLoss(nn.Module):
    """检测损失函数"""
    
    def __init__(self, cls_weight: float = 1.0, reg_weight: float = 1.0, center_weight: float = 0.5):
        super(DetectionLoss, self).__init__()
        self.cls_weight = cls_weight
        self.reg_weight = reg_weight
        self.center_weight = center_weight
        
        self.focal_loss = FocalLoss()
        self.reg_loss = nn.SmoothL1Loss()
        self.center_loss = nn.BCEWithLogitsLoss()
    
    def forward(self, predictions: Dict, targets: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        计算检测损失
        Args:
            predictions: 模型预测结果
            targets: 真实标签
        Returns:
            损失字典
        """
        cls_scores = predictions['cls_scores']
        bbox_preds = predictions['bbox_preds']
        centernesses = predictions['centernesses']
        
        # 这里简化处理，实际应该根据具体的检测框架实现
        # 假设已经有匹配好的正负样本
        
        total_cls_loss = 0
        total_reg_loss = 0
        total_center_loss = 0
        
        for cls_score, bbox_pred, centerness in zip(cls_scores, bbox_preds, centernesses):
            # 分类损失 (简化版本)
            batch_size = cls_score.size(0)
            dummy_cls_targets = torch.zeros(batch_size, dtype=torch.long, device=cls_score.device)
            cls_loss = self.focal_loss(cls_score.view(-1, cls_score.size(-1)), dummy_cls_targets.view(-1))
            
            # 回归损失 (简化版本)
            dummy_reg_targets = torch.zeros_like(bbox_pred)
            reg_loss = self.reg_loss(bbox_pred, dummy_reg_targets)
            
            # 中心度损失 (简化版本)
            dummy_center_targets = torch.zeros_like(centerness)
            center_loss = self.center_loss(centerness, dummy_center_targets)
            
            total_cls_loss += cls_loss
            total_reg_loss += reg_loss
            total_center_loss += center_loss
        
        # 加权总损失
        total_loss = (self.cls_weight * total_cls_loss + 
                     self.reg_weight * total_reg_loss + 
                     self.center_weight * total_center_loss)
        
        return {
            'total_loss': total_loss,
            'cls_loss': total_cls_loss,
            'reg_loss': total_reg_loss,
            'center_loss': total_center_loss
        }


class Trainer:
    """训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device(config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu'))
        
        # 创建模型
        self.model = FourierInfraredNet(
            input_channels=config.get('input_channels', 1),
            num_classes=config.get('num_classes', 1),
            base_channels=config.get('base_channels', 64)
        ).to(self.device)
        
        # 损失函数
        self.criterion = DetectionLoss(
            cls_weight=config.get('cls_weight', 1.0),
            reg_weight=config.get('reg_weight', 1.0),
            center_weight=config.get('center_weight', 0.5)
        )
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.get('learning_rate', 1e-4),
            weight_decay=config.get('weight_decay', 1e-4)
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.get('epochs', 100),
            eta_min=config.get('min_lr', 1e-6)
        )
        
        # 训练记录
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')
        
        # 创建保存目录
        self.save_dir = config.get('save_dir', 'checkpoints')
        os.makedirs(self.save_dir, exist_ok=True)
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        epoch_losses = {
            'total_loss': 0.0,
            'cls_loss': 0.0,
            'reg_loss': 0.0,
            'center_loss': 0.0
        }
        
        num_batches = len(train_loader)
        
        with tqdm(train_loader, desc="Training") as pbar:
            for batch_idx, (images, targets) in enumerate(pbar):
                images = images.to(self.device)
                
                # 前向传播
                predictions = self.model(images)
                
                # 计算损失
                losses = self.criterion(predictions, targets)
                
                # 反向传播
                self.optimizer.zero_grad()
                losses['total_loss'].backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                
                # 累计损失
                for key in epoch_losses:
                    epoch_losses[key] += losses[key].item()
                
                # 更新进度条
                pbar.set_postfix({
                    'loss': f"{losses['total_loss'].item():.4f}",
                    'cls': f"{losses['cls_loss'].item():.4f}",
                    'reg': f"{losses['reg_loss'].item():.4f}"
                })
        
        # 平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证"""
        self.model.eval()
        
        epoch_losses = {
            'total_loss': 0.0,
            'cls_loss': 0.0,
            'reg_loss': 0.0,
            'center_loss': 0.0
        }
        
        num_batches = len(val_loader)
        
        with torch.no_grad():
            with tqdm(val_loader, desc="Validation") as pbar:
                for images, targets in pbar:
                    images = images.to(self.device)
                    
                    # 前向传播
                    predictions = self.model(images)
                    
                    # 计算损失
                    losses = self.criterion(predictions, targets)
                    
                    # 累计损失
                    for key in epoch_losses:
                        epoch_losses[key] += losses[key].item()
                    
                    # 更新进度条
                    pbar.set_postfix({
                        'val_loss': f"{losses['total_loss'].item():.4f}"
                    })
        
        # 平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def save_checkpoint(self, epoch: int, val_loss: float, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'val_loss': val_loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'config': self.config
        }
        
        # 保存最新检查点
        checkpoint_path = os.path.join(self.save_dir, 'latest_checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.save_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"💾 保存最佳模型: {best_path}")
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader):
        """完整训练流程"""
        epochs = self.config.get('epochs', 100)
        
        print(f"🚀 开始训练，共{epochs}个epoch")
        print(f"📱 设备: {self.device}")
        print(f"🔧 模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(epochs):
            print(f"\n📅 Epoch {epoch+1}/{epochs}")
            
            # 训练
            train_losses = self.train_epoch(train_loader)
            self.train_losses.append(train_losses)
            
            # 验证
            val_losses = self.validate(val_loader)
            self.val_losses.append(val_losses)
            
            # 更新学习率
            self.scheduler.step()
            
            # 打印结果
            print(f"📊 训练损失: {train_losses['total_loss']:.4f}")
            print(f"📊 验证损失: {val_losses['total_loss']:.4f}")
            print(f"📈 学习率: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # 保存检查点
            is_best = val_losses['total_loss'] < self.best_val_loss
            if is_best:
                self.best_val_loss = val_losses['total_loss']
            
            self.save_checkpoint(epoch, val_losses['total_loss'], is_best)
            
            # 每10个epoch绘制损失曲线
            if (epoch + 1) % 10 == 0:
                self.plot_losses()
        
        print("🎉 训练完成！")
    
    def plot_losses(self):
        """绘制损失曲线"""
        epochs = range(1, len(self.train_losses) + 1)
        
        train_total = [loss['total_loss'] for loss in self.train_losses]
        val_total = [loss['total_loss'] for loss in self.val_losses]
        
        plt.figure(figsize=(10, 6))
        plt.plot(epochs, train_total, 'b-', label='Training Loss')
        plt.plot(epochs, val_total, 'r-', label='Validation Loss')
        plt.title('Training and Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        # 保存图像
        loss_plot_path = os.path.join(self.save_dir, 'loss_curves.png')
        plt.savefig(loss_plot_path)
        plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Train Fourier Infrared Detection Network')
    parser.add_argument('--data_root', type=str, default='../data', help='Data root directory')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--device', type=str, default='auto', help='Device to use')
    parser.add_argument('--save_dir', type=str, default='checkpoints', help='Save directory')
    
    args = parser.parse_args()
    
    # 设备选择
    if args.device == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = args.device
    
    # 配置
    config = {
        'data_root': args.data_root,
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'learning_rate': args.lr,
        'device': device,
        'save_dir': args.save_dir,
        'input_channels': 1,
        'num_classes': 1,
        'base_channels': 64,
        'weight_decay': 1e-4,
        'cls_weight': 1.0,
        'reg_weight': 1.0,
        'center_weight': 0.5
    }
    
    print("🔧 配置信息:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    # 创建数据集
    try:
        train_dataset = InfraredTargetDataset(args.data_root, split='train')
        val_dataset = InfraredTargetDataset(args.data_root, split='val')
        
        train_loader = DataLoader(
            train_dataset, 
            batch_size=args.batch_size, 
            shuffle=True, 
            num_workers=4,
            pin_memory=True
        )
        val_loader = DataLoader(
            val_dataset, 
            batch_size=args.batch_size, 
            shuffle=False, 
            num_workers=4,
            pin_memory=True
        )
        
        print(f"📊 数据集统计:")
        print(f"   训练集: {len(train_dataset)}个样本")
        print(f"   验证集: {len(val_dataset)}个样本")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return
    
    # 创建训练器并开始训练
    try:
        trainer = Trainer(config)
        trainer.train(train_loader, val_loader)
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
