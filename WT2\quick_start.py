#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Start Script for Fourier Infrared Detection
快速启动脚本，提供一站式的系统使用体验

主要功能：
1. 系统状态检查
2. 数据集配置
3. 模型训练
4. 模型演示
5. 结果查看

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import json
import subprocess
from pathlib import Path


class QuickStart:
    """快速启动类"""
    
    def __init__(self):
        self.config_file = "dataset_config.json"
        self.project_root = Path(__file__).parent
    
    def welcome(self):
        """显示欢迎信息"""
        print("🚀 傅里叶红外小目标检测系统 - 快速启动")
        print("=" * 60)
        print("欢迎使用基于傅里叶分析的红外小目标检测系统！")
        print("本工具将引导您完成系统的配置和使用。")
        print("=" * 60)
    
    def check_system(self):
        """检查系统状态"""
        print("\n🔍 系统状态检查...")
        
        # 检查Python版本
        python_version = sys.version.split()[0]
        print(f"🐍 Python版本: {python_version}")
        
        # 检查PyTorch
        try:
            import torch
            print(f"🔥 PyTorch版本: {torch.__version__}")
            print(f"💾 CUDA可用: {'是' if torch.cuda.is_available() else '否'}")
            if torch.cuda.is_available():
                print(f"🎮 GPU设备: {torch.cuda.get_device_name(0)}")
        except ImportError:
            print("❌ PyTorch未安装，请先安装PyTorch")
            return False
        
        # 检查其他依赖
        required_packages = ['numpy', 'matplotlib', 'opencv-python', 'pillow', 'tqdm']
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'opencv-python':
                    import cv2
                elif package == 'pillow':
                    import PIL
                else:
                    __import__(package)
                print(f"✅ {package}: 已安装")
            except ImportError:
                print(f"❌ {package}: 未安装")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
            install = input("是否自动安装? (y/n): ").strip().lower()
            if install in ['y', 'yes']:
                try:
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
                    print("✅ 依赖包安装完成")
                except subprocess.CalledProcessError:
                    print("❌ 依赖包安装失败，请手动安装")
                    return False
            else:
                return False
        
        # 运行模块测试
        print("\n🧪 运行模块测试...")
        try:
            result = subprocess.run([sys.executable, 'test_modules.py'], 
                                  capture_output=True, text=True, cwd=self.project_root)
            if result.returncode == 0:
                print("✅ 所有模块测试通过")
                return True
            else:
                print("❌ 模块测试失败")
                print(result.stdout)
                print(result.stderr)
                return False
        except Exception as e:
            print(f"❌ 运行测试失败: {e}")
            return False
    
    def load_config(self):
        """加载配置"""
        config_path = self.project_root / self.config_file
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"❌ 加载配置失败: {e}")
        return None
    
    def setup_dataset(self):
        """设置数据集"""
        print("\n📁 数据集配置...")
        
        config = self.load_config()
        if config:
            dataset_path = config.get('dataset_path')
            print(f"✅ 找到已配置的数据集: {dataset_path}")
            
            use_existing = input("是否使用现有配置? (y/n): ").strip().lower()
            if use_existing in ['y', 'yes']:
                return dataset_path
        
        # 运行数据集配置脚本
        try:
            result = subprocess.run([sys.executable, 'setup_dataset.py'], 
                                  cwd=self.project_root)
            if result.returncode == 0:
                # 重新加载配置
                config = self.load_config()
                if config:
                    return config.get('dataset_path')
            return None
        except Exception as e:
            print(f"❌ 数据集配置失败: {e}")
            return None
    
    def train_model(self, dataset_path):
        """训练模型"""
        print("\n🎯 模型训练...")
        
        print("请选择训练模式:")
        print("1. 快速训练 (10个epoch，适合测试)")
        print("2. 标准训练 (100个epoch)")
        print("3. 自定义训练参数")
        print("4. 跳过训练")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '4':
            print("⏭️ 跳过训练")
            return True
        
        # 构建训练命令
        train_cmd = [sys.executable, 'experiments/train_fourier_detector.py', 
                    '--data_root', dataset_path]
        
        if choice == '1':
            train_cmd.extend(['--epochs', '10', '--batch_size', '4'])
            print("🚀 开始快速训练...")
        elif choice == '2':
            train_cmd.extend(['--epochs', '100', '--batch_size', '8'])
            print("🚀 开始标准训练...")
        elif choice == '3':
            epochs = input("请输入训练轮数 (默认100): ").strip() or '100'
            batch_size = input("请输入批大小 (默认8): ").strip() or '8'
            lr = input("请输入学习率 (默认1e-4): ").strip() or '1e-4'
            
            train_cmd.extend(['--epochs', epochs, '--batch_size', batch_size, '--lr', lr])
            print(f"🚀 开始自定义训练 (epochs={epochs}, batch_size={batch_size}, lr={lr})...")
        else:
            print("❌ 无效选择")
            return False
        
        try:
            result = subprocess.run(train_cmd, cwd=self.project_root)
            if result.returncode == 0:
                print("✅ 训练完成")
                return True
            else:
                print("❌ 训练失败")
                return False
        except Exception as e:
            print(f"❌ 训练过程出错: {e}")
            return False
    
    def demo_model(self):
        """演示模型"""
        print("\n🎨 模型演示...")
        
        # 检查是否有训练好的模型
        model_paths = [
            'checkpoints/best_model.pth',
            'checkpoints/latest_checkpoint.pth'
        ]
        
        available_models = []
        for model_path in model_paths:
            full_path = self.project_root / model_path
            if full_path.exists():
                available_models.append(str(full_path))
        
        if not available_models:
            print("⚠️ 未找到训练好的模型")
            print("您可以:")
            print("1. 先进行模型训练")
            print("2. 使用随机初始化的模型进行演示 (仅用于测试)")
            
            choice = input("请选择 (1/2): ").strip()
            if choice == '1':
                return False
            elif choice == '2':
                model_path = 'checkpoints/best_model.pth'  # 使用默认路径，即使文件不存在
            else:
                return False
        else:
            model_path = available_models[0]
            print(f"✅ 找到模型: {model_path}")
        
        # 运行演示
        try:
            demo_cmd = [sys.executable, 'experiments/demo.py', 
                       '--model_path', model_path, '--interactive']
            
            result = subprocess.run(demo_cmd, cwd=self.project_root)
            if result.returncode == 0:
                print("✅ 演示完成")
                return True
            else:
                print("❌ 演示失败")
                return False
        except Exception as e:
            print(f"❌ 演示过程出错: {e}")
            return False
    
    def view_results(self):
        """查看结果"""
        print("\n📊 查看结果...")
        
        results_dirs = ['results', 'demo_results', 'checkpoints']
        found_results = []
        
        for results_dir in results_dirs:
            full_path = self.project_root / results_dir
            if full_path.exists() and any(full_path.iterdir()):
                found_results.append(str(full_path))
        
        if found_results:
            print("📁 找到以下结果目录:")
            for i, result_dir in enumerate(found_results, 1):
                print(f"  {i}. {result_dir}")
            
            try:
                # 在Windows上打开文件夹
                if os.name == 'nt':
                    os.startfile(found_results[0])
                # 在macOS上打开文件夹
                elif sys.platform == 'darwin':
                    subprocess.run(['open', found_results[0]])
                # 在Linux上打开文件夹
                else:
                    subprocess.run(['xdg-open', found_results[0]])
                
                print(f"✅ 已打开结果目录: {found_results[0]}")
            except Exception as e:
                print(f"❌ 打开目录失败: {e}")
                print(f"请手动查看: {found_results[0]}")
        else:
            print("❌ 未找到结果文件")
    
    def main_menu(self):
        """主菜单"""
        while True:
            print("\n🎯 主菜单")
            print("=" * 30)
            print("1. 🔍 系统检查")
            print("2. 📁 数据集配置")
            print("3. 🎯 模型训练")
            print("4. 🎨 模型演示")
            print("5. 📊 查看结果")
            print("6. 🚪 退出")
            
            choice = input("\n请输入选择 (1-6): ").strip()
            
            if choice == '1':
                self.check_system()
            elif choice == '2':
                self.setup_dataset()
            elif choice == '3':
                dataset_path = self.setup_dataset()
                if dataset_path:
                    self.train_model(dataset_path)
            elif choice == '4':
                self.demo_model()
            elif choice == '5':
                self.view_results()
            elif choice == '6':
                print("👋 感谢使用，再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
    
    def run(self):
        """运行快速启动流程"""
        self.welcome()
        
        # 自动运行完整流程
        auto_run = input("\n🤖 是否运行自动化流程? (y/n): ").strip().lower()
        
        if auto_run in ['y', 'yes']:
            print("\n🚀 开始自动化流程...")
            
            # 1. 系统检查
            if not self.check_system():
                print("❌ 系统检查失败，请解决问题后重试")
                return
            
            # 2. 数据集配置
            dataset_path = self.setup_dataset()
            if not dataset_path:
                print("❌ 数据集配置失败")
                return
            
            # 3. 询问是否训练
            train = input("\n🎯 是否进行模型训练? (y/n): ").strip().lower()
            if train in ['y', 'yes']:
                self.train_model(dataset_path)
            
            # 4. 询问是否演示
            demo = input("\n🎨 是否进行模型演示? (y/n): ").strip().lower()
            if demo in ['y', 'yes']:
                self.demo_model()
            
            print("\n🎉 自动化流程完成！")
        else:
            # 进入交互式菜单
            self.main_menu()


def main():
    """主函数"""
    try:
        quick_start = QuickStart()
        quick_start.run()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
