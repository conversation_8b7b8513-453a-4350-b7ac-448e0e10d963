#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo Script for Fourier-based Infrared Small Target Detection
基于傅里叶分析的红外小目标检测演示脚本

主要功能：
1. 加载预训练模型
2. 处理单张图像或批量图像
3. 可视化检测结果
4. 性能分析和统计
5. 与传统方法对比

作者：AI Assistant
日期：2025-06-30
版本：v1.0
"""

import os
import sys
import json
import time
import argparse
from typing import Dict, List, Tuple, Optional

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import cv2
from PIL import Image
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.fourier_infrared_net import FourierInfraredNet
from utils.infrared_preprocessing import InfraredPreprocessingPipeline
from utils.fourier_transforms import FourierTransformUtils, FrequencyDomainVisualizer


class FourierInfraredDetector:
    """傅里叶红外小目标检测器"""
    
    def __init__(self, model_path: str, device: str = 'auto', config: Dict = None):
        """
        初始化检测器
        Args:
            model_path: 模型路径
            device: 设备
            config: 配置
        """
        # 设备选择
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 默认配置
        self.config = config or {
            'input_channels': 1,
            'num_classes': 1,
            'base_channels': 64,
            'conf_threshold': 0.5,
            'nms_threshold': 0.5
        }
        
        # 加载模型
        self.model = self._load_model(model_path)
        
        # 预处理器
        self.preprocessor = InfraredPreprocessingPipeline()
        
        # 可视化器
        self.visualizer = FrequencyDomainVisualizer()
        
        print(f"🚀 傅里叶红外检测器初始化完成")
        print(f"📱 设备: {self.device}")
        print(f"🔧 模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def _load_model(self, model_path: str) -> torch.nn.Module:
        """加载模型"""
        try:
            # 创建模型
            model = FourierInfraredNet(
                input_channels=self.config['input_channels'],
                num_classes=self.config['num_classes'],
                base_channels=self.config['base_channels']
            )
            
            # 加载权重
            if os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"✅ 加载预训练模型: {model_path}")
                else:
                    model.load_state_dict(checkpoint)
                    print(f"✅ 加载模型权重: {model_path}")
            else:
                print(f"⚠️ 模型文件不存在，使用随机初始化: {model_path}")
            
            model.to(self.device)
            model.eval()
            
            return model
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def preprocess_image(self, image_path: str) -> Tuple[torch.Tensor, np.ndarray]:
        """
        预处理图像
        Args:
            image_path: 图像路径
        Returns:
            (processed_tensor, original_image)
        """
        try:
            # 加载原始图像
            if isinstance(image_path, str):
                original_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
                if original_image is None:
                    # 尝试用PIL加载
                    pil_image = Image.open(image_path).convert('L')
                    original_image = np.array(pil_image)
            else:
                original_image = image_path
            
            # 转换为tensor
            image_tensor = torch.from_numpy(original_image.astype(np.float32) / 255.0)
            image_tensor = image_tensor.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
            
            # 预处理
            processed_tensor = self.preprocessor.preprocess(image_tensor, training=False)
            processed_tensor = processed_tensor.to(self.device)
            
            return processed_tensor, original_image
            
        except Exception as e:
            print(f"❌ 图像预处理失败: {e}")
            raise
    
    def detect(self, image_tensor: torch.Tensor) -> Dict:
        """
        检测小目标
        Args:
            image_tensor: 预处理后的图像tensor
        Returns:
            检测结果
        """
        with torch.no_grad():
            # 前向传播
            start_time = time.time()
            predictions = self.model(image_tensor)
            inference_time = time.time() - start_time
            
            # 后处理
            detections = self._postprocess_predictions(predictions)
            
            return {
                'detections': detections,
                'inference_time': inference_time,
                'raw_predictions': predictions
            }
    
    def _postprocess_predictions(self, predictions: Dict) -> List[Dict]:
        """
        后处理预测结果
        Args:
            predictions: 模型原始预测
        Returns:
            检测框列表
        """
        detections = []
        
        cls_scores = predictions['cls_scores']
        bbox_preds = predictions['bbox_preds']
        centernesses = predictions['centernesses']
        
        # 简化的后处理 - 实际应该实现完整的NMS等
        for i, (cls_score, bbox_pred, centerness) in enumerate(zip(cls_scores, bbox_preds, centernesses)):
            # 获取高置信度的检测
            batch_size, num_anchors, height, width = cls_score.shape
            
            # 展平并获取置信度
            cls_flat = torch.sigmoid(cls_score).view(batch_size, -1)
            bbox_flat = bbox_pred.view(batch_size, -1, 4)
            center_flat = torch.sigmoid(centerness).view(batch_size, -1)
            
            # 简单阈值过滤
            conf_threshold = self.config.get('conf_threshold', 0.5)
            valid_mask = cls_flat > conf_threshold
            
            for b in range(batch_size):
                valid_indices = torch.where(valid_mask[b])[0]
                
                for idx in valid_indices:
                    # 计算在原图中的位置
                    h_idx = idx // width
                    w_idx = idx % width
                    
                    # 获取检测信息
                    confidence = cls_flat[b, idx].item()
                    bbox = bbox_flat[b, idx].cpu().numpy()
                    center_score = center_flat[b, idx].item()
                    
                    # 简单的坐标转换 (需要根据实际anchor设计调整)
                    scale_factor = 2 ** (i + 2)  # 假设每层下采样2倍
                    x = w_idx * scale_factor
                    y = h_idx * scale_factor
                    w = max(bbox[2], 1) * scale_factor
                    h = max(bbox[3], 1) * scale_factor
                    
                    detections.append({
                        'bbox': [x, y, w, h],
                        'confidence': confidence,
                        'center_score': center_score,
                        'class': 0
                    })
        
        # 简单的NMS (实际应该使用更完善的实现)
        detections = self._simple_nms(detections)
        
        return detections
    
    def _simple_nms(self, detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """简单的非极大值抑制"""
        if not detections:
            return []
        
        # 按置信度排序
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        keep = []
        while detections:
            current = detections.pop(0)
            keep.append(current)
            
            # 移除与当前框IoU过高的框
            remaining = []
            for det in detections:
                if self._calculate_iou(current['bbox'], det['bbox']) < iou_threshold:
                    remaining.append(det)
            detections = remaining
        
        return keep
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2
        
        # 转换为 (x1, y1, x2, y2) 格式
        box1_coords = [x1, y1, x1 + w1, y1 + h1]
        box2_coords = [x2, y2, x2 + w2, y2 + h2]
        
        # 计算交集
        x_left = max(box1_coords[0], box2_coords[0])
        y_top = max(box1_coords[1], box2_coords[1])
        x_right = min(box1_coords[2], box2_coords[2])
        y_bottom = min(box1_coords[3], box2_coords[3])
        
        if x_right < x_left or y_bottom < y_top:
            return 0.0
        
        intersection = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def visualize_results(self, original_image: np.ndarray, detections: List[Dict], 
                         save_path: str = None, show_frequency_analysis: bool = True) -> np.ndarray:
        """
        可视化检测结果
        Args:
            original_image: 原始图像
            detections: 检测结果
            save_path: 保存路径
            show_frequency_analysis: 是否显示频域分析
        Returns:
            可视化图像
        """
        # 创建可视化图像
        if len(original_image.shape) == 2:
            vis_image = cv2.cvtColor(original_image, cv2.COLOR_GRAY2BGR)
        else:
            vis_image = original_image.copy()
        
        # 绘制检测框
        for det in detections:
            bbox = det['bbox']
            confidence = det['confidence']
            
            x, y, w, h = [int(v) for v in bbox]
            
            # 绘制矩形框
            color = (0, 255, 0)  # 绿色
            thickness = 2
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制置信度
            label = f"Target: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            cv2.rectangle(vis_image, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            cv2.putText(vis_image, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 创建综合可视化
        if show_frequency_analysis:
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            
            # 原始图像
            axes[0, 0].imshow(original_image, cmap='gray')
            axes[0, 0].set_title('Original Image')
            axes[0, 0].axis('off')
            
            # 检测结果
            axes[0, 1].imshow(cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB))
            axes[0, 1].set_title(f'Detection Results ({len(detections)} targets)')
            axes[0, 1].axis('off')
            
            # 频谱分析
            f_transform = np.fft.fft2(original_image)
            f_shift = np.fft.fftshift(f_transform)
            magnitude_spectrum = np.log(np.abs(f_shift) + 1)
            
            axes[0, 2].imshow(magnitude_spectrum, cmap='gray')
            axes[0, 2].set_title('Frequency Spectrum')
            axes[0, 2].axis('off')
            
            # 预处理后的图像
            processed_tensor, _ = self.preprocess_image(original_image)
            processed_image = processed_tensor.squeeze().cpu().numpy()
            
            axes[1, 0].imshow(processed_image, cmap='gray')
            axes[1, 0].set_title('Preprocessed Image')
            axes[1, 0].axis('off')
            
            # 特征图可视化 (如果有的话)
            axes[1, 1].imshow(original_image, cmap='gray')
            axes[1, 1].set_title('Feature Visualization')
            axes[1, 1].axis('off')
            
            # 统计信息
            axes[1, 2].axis('off')
            stats_text = f"""Detection Statistics:
            
Total Targets: {len(detections)}
Avg Confidence: {np.mean([d['confidence'] for d in detections]):.3f}
Max Confidence: {max([d['confidence'] for d in detections]) if detections else 0:.3f}
Min Confidence: {min([d['confidence'] for d in detections]) if detections else 0:.3f}

Image Size: {original_image.shape}
Processing Device: {self.device}
"""
            axes[1, 2].text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"💾 可视化结果保存至: {save_path}")
            
            plt.show()
        
        return vis_image
    
    def process_single_image(self, image_path: str, save_dir: str = None) -> Dict:
        """
        处理单张图像
        Args:
            image_path: 图像路径
            save_dir: 保存目录
        Returns:
            处理结果
        """
        print(f"🖼️ 处理图像: {os.path.basename(image_path)}")
        
        # 预处理
        processed_tensor, original_image = self.preprocess_image(image_path)
        
        # 检测
        results = self.detect(processed_tensor)
        
        # 可视化
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            save_path = os.path.join(save_dir, f"{base_name}_result.png")
        else:
            save_path = None
        
        vis_image = self.visualize_results(
            original_image, 
            results['detections'], 
            save_path=save_path
        )
        
        # 打印结果
        print(f"✅ 检测完成:")
        print(f"   检测到 {len(results['detections'])} 个目标")
        print(f"   推理时间: {results['inference_time']:.3f}s")
        
        return {
            'detections': results['detections'],
            'inference_time': results['inference_time'],
            'visualization': vis_image,
            'original_image': original_image
        }


def get_image_path_interactive():
    """交互式获取图像路径"""
    print("🖼️ 图像路径配置")
    print("=" * 50)

    while True:
        print("\n请选择图像输入方式:")
        print("1. 手动输入图像文件路径")
        print("2. 手动输入图像文件夹路径")
        print("3. 浏览并选择现有图像")
        print("4. 使用WT数据集中的图像")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == '1':
            image_path = input("\n请输入图像文件路径: ").strip()
            if image_path:
                image_path = image_path.strip('"\'')
                if os.path.isfile(image_path):
                    print(f"✅ 图像文件确认: {image_path}")
                    return image_path
                else:
                    print(f"❌ 文件不存在: {image_path}")
                    continue
            else:
                print("❌ 路径不能为空")
                continue

        elif choice == '2':
            image_path = input("\n请输入图像文件夹路径: ").strip()
            if image_path:
                image_path = image_path.strip('"\'')
                if os.path.isdir(image_path):
                    # 检查文件夹中是否有图像文件
                    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
                    image_files = []
                    for ext in image_extensions:
                        image_files.extend(glob.glob(os.path.join(image_path, f"*{ext}")))
                        image_files.extend(glob.glob(os.path.join(image_path, f"*{ext.upper()}")))

                    if image_files:
                        print(f"✅ 图像文件夹确认: {image_path} (找到 {len(image_files)} 张图像)")
                        return image_path
                    else:
                        print(f"❌ 文件夹中没有找到图像文件: {image_path}")
                        continue
                else:
                    print(f"❌ 文件夹不存在: {image_path}")
                    continue
            else:
                print("❌ 路径不能为空")
                continue

        elif choice == '3':
            print("\n🔍 搜索可能的图像文件...")
            possible_images = []

            # 搜索当前目录及相关目录中的图像文件
            search_dirs = ['.', '..', '../data', '../data/WT']
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']

            for search_dir in search_dirs:
                if os.path.exists(search_dir):
                    for ext in image_extensions:
                        possible_images.extend(glob.glob(os.path.join(search_dir, f"**/*{ext}"), recursive=True))
                        possible_images.extend(glob.glob(os.path.join(search_dir, f"**/*{ext.upper()}"), recursive=True))

            # 去重并限制数量
            possible_images = list(set(possible_images))[:20]  # 最多显示20个

            if possible_images:
                print(f"\n找到 {len(possible_images)} 个图像文件:")
                for i, path in enumerate(possible_images, 1):
                    print(f"  {i}. {os.path.basename(path)} ({path})")

                try:
                    idx = int(input(f"\n请选择图像 (1-{len(possible_images)}): ")) - 1
                    if 0 <= idx < len(possible_images):
                        image_path = possible_images[idx]
                        print(f"✅ 选择图像: {image_path}")
                        return image_path
                    else:
                        print("❌ 选择无效")
                        continue
                except ValueError:
                    print("❌ 请输入有效数字")
                    continue
            else:
                print("❌ 未找到图像文件，请手动输入")
                continue

        elif choice == '4':
            # 查找WT数据集中的图像
            wt_paths = ['../data/WT', '../../data/WT', '../../../data/WT']
            wt_images = []

            for wt_path in wt_paths:
                if os.path.exists(wt_path):
                    for ext in ['.png', '.jpg', '.jpeg']:
                        pattern = os.path.join(wt_path, f"**/*{ext}")
                        wt_images.extend(glob.glob(pattern, recursive=True))

            if wt_images:
                wt_images = wt_images[:10]  # 最多显示10个
                print(f"\n找到 {len(wt_images)} 个WT数据集图像:")
                for i, path in enumerate(wt_images, 1):
                    print(f"  {i}. {os.path.basename(path)}")

                try:
                    idx = int(input(f"\n请选择图像 (1-{len(wt_images)}): ")) - 1
                    if 0 <= idx < len(wt_images):
                        image_path = wt_images[idx]
                        print(f"✅ 选择WT图像: {image_path}")
                        return image_path
                    else:
                        print("❌ 选择无效")
                        continue
                except ValueError:
                    print("❌ 请输入有效数字")
                    continue
            else:
                print("❌ 未找到WT数据集图像")
                continue
        else:
            print("❌ 无效选择，请重新输入")
            continue


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Fourier Infrared Detection Demo')
    parser.add_argument('--model_path', type=str, default='checkpoints/best_model.pth',
                       help='Path to trained model')
    parser.add_argument('--image_path', type=str, default=None,
                       help='Path to input image or directory')
    parser.add_argument('--save_dir', type=str, default='demo_results',
                       help='Directory to save results')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto/cpu/cuda)')
    parser.add_argument('--conf_threshold', type=float, default=0.5,
                       help='Confidence threshold')
    parser.add_argument('--interactive', action='store_true',
                       help='Interactive mode for image path selection')

    args = parser.parse_args()

    # 如果没有指定图像路径或使用交互模式，则交互式获取
    if args.image_path is None or args.interactive:
        args.image_path = get_image_path_interactive()
    
    # 配置
    config = {
        'input_channels': 1,
        'num_classes': 1,
        'base_channels': 64,
        'conf_threshold': args.conf_threshold,
        'nms_threshold': 0.5
    }
    
    print("🚀 傅里叶红外小目标检测演示")
    print("=" * 50)
    
    # 创建检测器
    try:
        detector = FourierInfraredDetector(args.model_path, args.device, config)
    except Exception as e:
        print(f"❌ 检测器创建失败: {e}")
        return
    
    # 处理图像
    try:
        if os.path.isfile(args.image_path):
            # 单张图像
            result = detector.process_single_image(args.image_path, args.save_dir)
            
        elif os.path.isdir(args.image_path):
            # 批量处理
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
            image_files = []
            
            for ext in image_extensions:
                image_files.extend(glob.glob(os.path.join(args.image_path, f"*{ext}")))
                image_files.extend(glob.glob(os.path.join(args.image_path, f"*{ext.upper()}")))
            
            print(f"📁 找到 {len(image_files)} 张图像")
            
            total_detections = 0
            total_time = 0
            
            for image_file in tqdm(image_files, desc="处理图像"):
                result = detector.process_single_image(image_file, args.save_dir)
                total_detections += len(result['detections'])
                total_time += result['inference_time']
            
            print(f"\n📊 批量处理统计:")
            print(f"   总图像数: {len(image_files)}")
            print(f"   总检测数: {total_detections}")
            print(f"   平均检测数: {total_detections/len(image_files):.1f}")
            print(f"   总处理时间: {total_time:.2f}s")
            print(f"   平均处理时间: {total_time/len(image_files):.3f}s")
            
        else:
            print(f"❌ 输入路径无效: {args.image_path}")
            
    except Exception as e:
        print(f"❌ 图像处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import glob
    main()
